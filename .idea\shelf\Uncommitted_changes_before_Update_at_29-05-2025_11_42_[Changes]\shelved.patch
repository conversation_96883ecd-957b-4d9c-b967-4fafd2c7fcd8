Index: src/main/resources/application-dev.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>spring.jpa.hibernate.ddl-auto=update\r\n#spring.jpa.hibernate.ddl-auto=create-drop\r\n#spring.jpa.hibernate.ddl-auto=create\r\n#spring.jpa.hibernate.ddl-auto=none\r\nspring.datasource.url=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# application.baseFrontendUrl=http://app.localhost:3000\r\napplication.baseFrontendUrl=http://localhost:3000\r\nmanagement.endpoints.web.exposure.include=health,info,metrics\r\n# Optionally, configure access to the health endpoint\r\nmanagement.endpoint.health.probe.enabled=true\r\nmanagement.endpoints.web.base-path=/actuator\r\nmanagement.endpoint.health.show-details=always\r\napplication.ticketEmail=<EMAIL>\r\napplication.certificatecode=JB\r\napplication.name=Job Portal\r\napplication.description=Portal for Applying Jobs under several department\r\napplication.version=1.0.0\r\napp.base-url=https://www.groglojobs.co.uk\r\nserver.port=8080\r\nspring.sql.init.mode=embedded\r\n#spring.sql.init.mode=never\r\napp.database.initialize=true\r\nspring.jpa.open-in-view=false\r\nspring.jpa.defer-datasource-initialization=true\r\n#spring.flyway.enabled=true\r\n#spring.jpa.defer-datasource-initialization=false\r\nspring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER\r\nspring.servlet.multipart.max-file-size=-1\r\nspring.servlet.multipart.max-request-size=-1\r\napplication.aws.bucketname=ebrainyvideostreaming\r\napplication.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com\r\napplication.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net\r\napplication.aws.accessKey=********************\r\napplication.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM\r\napplication.aws.region=eu-north-1\r\napplication.aws.secretName=stripe\r\n\r\napplication.email=<EMAIL>\r\n#email.domain.from=<EMAIL>\r\n\r\n# Social media links\r\nsocial.facebook.url=https://www.facebook.com/groglojobs\r\nsocial.linkedin.url=https://www.linkedin.com/company/groglojobs\r\nsocial.twitter.url=https://twitter.com/groglojobs\r\n\r\n# Social media icons\r\nsocial.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png\r\nsocial.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png\r\nsocial.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png\r\n\r\n\r\n#mail\r\nemail.provider=domain\r\n\r\n# gmail\r\nspring.mail.host=smtp.gmail.com\r\nspring.mail.port=587\r\nspring.mail.username=<EMAIL>\r\nspring.mail.password=aoamtqqqspnwjuvu\r\n#domain\r\n#email.domain.host=smtp.zoho.in\r\n#email.domain.port=465\r\n#email.domain.username=<EMAIL>\r\n#email.domain.password=rfxVXje1NXUz\r\n#email.domain.from=<EMAIL>\r\nemail.domain.host=smtp.zoho.eu\r\nemail.domain.port=465\r\nemail.domain.username=<EMAIL>\r\nemail.domain.password=vNsnxLakWUnr\r\nemail.domain.from=<EMAIL>\r\n\r\n\r\n\r\nspring.mail.properties.mail.smtp.auth=true\r\nspring.mail.properties.mail.smtp.starttls.enable=true\r\n\r\n#sms\r\nsms.PHONE_NUMBER=+16203901757\r\n#payment\r\n# Razorpay\r\nstripe.webhook.signing.currency=GBP\r\napplication.multiCurrency=false\r\napplication.multiCurrencyList=USD,GBP,EUR\r\n#security\r\nspring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com\r\nspring.security.oauth2.client.registration.google.clientSecret=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\r\nspring.security.oauth2.client.registration.google.scope=email, profile\r\nspring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.registration.facebook.clientId=1456102035268496\r\nspring.security.oauth2.client.registration.facebook.clientSecret=********************************\r\nspring.security.oauth2.client.registration.facebook.scope=email, public_profile\r\nspring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth\r\nspring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token\r\n#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified\r\napp.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1\r\napp.auth.tokenExpirationMsec=864000000\r\napp.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*\r\napp.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect\r\n#logs\r\nlogging.level.root=INFO\r\nlogging.config=classpath:logback-spring.xml\r\nlogging.level.org.springframework=INFO\r\nlogging.level.org.springframework.boot=INFO\r\nlogging.level.org.springframework.boot.autoconfigure=INFO\r\nlogging.level.org.springframework.boot.context=INFO\r\nlogging.level.org.springframework.boot.devtools=INFO\r\nlogging.level.org.springframework.web=INFO\r\nspring.devtools.restart.enabled=false\r\n\r\n# stripe\r\nstripe.api.key=sk_test_51R6oOD4YEQBprOqE5njXOiLJbv3yOi3XZUVKOgMUpeSFBxG1BB6h9DMUR3QRJYkCKisdksd1UmAxEp5Y5OVNKTG1002yeP6bV1\r\nstripe.webhook.signing.secret=whsec_3e18ca3d7014104d8b29a889e2ead2513989296abded91963fd6ae676d931ba6\r\nstripe.price.standard.monthly=price_1R78mJ4YEQBprOqE5tTXs86x\r\nstripe.price.standard.yearly=price_standard_yearly_id\r\nstripe.price.premium.monthly=price_1R78mw4YEQBprOqEDP69lLFW\r\nstripe.price.premium.yearly=price_premium_yearly_id\r\nstripe.price.enterprise.monthly=price_enterprise_monthly_id\r\nstripe.price.enterprise.yearly=price_enterprise_yearly_id\r\n\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/src/main/resources/application-dev.properties b/src/main/resources/application-dev.properties
--- a/src/main/resources/application-dev.properties	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/resources/application-dev.properties	(date 1748428304812)
@@ -4,7 +4,7 @@
 #spring.jpa.hibernate.ddl-auto=none
 spring.datasource.url=*************************************
 spring.datasource.username=root
-spring.datasource.password=system123#
+spring.datasource.password=root
 spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
 #spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
 spring.jpa.show-sql=true
Index: src/main/java/com/job/jobportal/service/UserService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.job.jobportal.service;\r\n\r\nimport com.job.jobportal.dto.ChangeUserDetailsDTO;\r\nimport com.job.jobportal.dto.MarketingUserDTO;\r\nimport com.job.jobportal.dto.UserDTO;\r\nimport com.job.jobportal.dto.UserListDTO;\r\nimport com.job.jobportal.model.AccountDetails;\r\nimport com.job.jobportal.model.Registereduser;\r\nimport com.job.jobportal.model.Roles;\r\nimport com.job.jobportal.repository.AccountDetailsRepo;\r\nimport com.job.jobportal.repository.PasswordResetTokenRepo;\r\nimport com.job.jobportal.repository.RegisteruserRepository;\r\nimport com.job.jobportal.repository.RolesRepository;\r\nimport com.job.jobportal.security.AuthProvider;\r\nimport com.job.jobportal.security.PasswordResetToken;\r\nimport com.job.jobportal.security.UserPrincipal;\r\nimport com.job.jobportal.security.oauth2.user.OAuth2UserInfo;\r\nimport com.job.jobportal.util.ConstantsUtil;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.data.domain.Page;\r\nimport org.springframework.data.domain.PageImpl;\r\nimport org.springframework.data.domain.PageRequest;\r\nimport org.springframework.data.domain.Pageable;\r\nimport org.springframework.data.domain.Sort;\r\nimport org.springframework.security.core.context.SecurityContextHolder;\r\nimport org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;\r\nimport org.springframework.stereotype.Service;\r\n\r\nimport java.sql.Date;\r\nimport java.sql.Timestamp;\r\nimport java.util.*;\r\nimport java.util.stream.Collectors;\r\n\r\n//import com.gamedev.security.UserPrinciple;\r\n\r\n\r\n@Service\r\npublic class UserService {\r\n\r\n\r\n    @Autowired\r\n    RegisteruserRepository userRepo;\r\n\r\n    @Autowired\r\n    AccountDetailsRepo accountDetailsRepo;\r\n\r\n\r\n    @Autowired\r\n    PasswordResetTokenRepo passwordResetTokenRepo;\r\n    @Autowired\r\n    EmailService emailService;\r\n    @Autowired\r\n    private RolesRepository rolesRepo;\r\n    @Value(\"${application.email}\")\r\n    private String email;\r\n\r\n    @Value(\"${application.name}\")\r\n    private String name;\r\n\r\n    private static final Logger logger = LoggerFactory.getLogger(UserService.class);\r\n\r\n\r\n    public Registereduser repoSaveUser(Registereduser user) {\r\n        Registereduser userSaved = userRepo.save(user);\r\n        String body = \"The following user is registered for Amildham :\\n\" + \"\\n\" +\r\n                \"First Name -\" + user.getFirstname() + \"\\n\" + \"\\n\" +\r\n                \"Last Name -\" + user.getLastname() + \"\\n\" + \"\\n\" +\r\n                \"Email -\" + user.getEmail() + \"\\n\" + \"\\n\" +\r\n                \"Mobile Number -\" + user.getMobileno() + \"\\n\" + \"\\n\";\r\n\r\n        emailService.sendEmail(email, email, \"New User Registered in \" + name, body);\r\n        return userSaved;\r\n    }\r\n\r\n\r\n    public Registereduser saveUser(UserDTO user) throws Exception {\r\n        try {\r\n\r\n            if (userRepo.findByEmail(user.getEmailId()).isPresent()) {\r\n                throw new Exception(\"There is an account with that email address: \" + user.getEmailId());\r\n            } else {\r\n                Registereduser regUser = new Registereduser();\r\n                Calendar cal = Calendar.getInstance();\r\n                regUser.setCreatedOn(new Timestamp(cal.getTimeInMillis()));\r\n                regUser.setEmail(user.getEmailId());\r\n                regUser.setFirstname(user.getFirstName());\r\n                regUser.setLastname(user.getLastName());\r\n                regUser.setMobileno(user.getMobileNo());\r\n                //regUser.setPassword(bcryptEncoder.encode(user.getPassword()));\r\n                regUser.setPassword(user.getPassword());\r\n                regUser.setUsername(user.getEmailId());\r\n                Set<Roles> roleSet = new HashSet<>();\r\n                roleSet.add(rolesRepo.findByRolename(user.getRoleName()));\r\n                regUser.setRoles(roleSet);\r\n\r\n\r\n                regUser.setRefreshToken(user.getRefreshToken());\r\n                regUser.setProvider(user.getAuthType());\r\n                regUser.setHasPassword(user.getHasPassword());\r\n                AccountDetails accountDetails = new AccountDetails();\r\n                accountDetails.setIsPremiumAccount(ConstantsUtil.PREMIUM_ACCOUNT_ACTIVE_INACTIVE);\r\n                accountDetails.setIsDeleteScheduled(ConstantsUtil.DELETE_ACCOUNT_ACTIVE_INACTIVE);\r\n                accountDetails.setStorageUse(0L);\r\n                accountDetails.setIsActive(ConstantsUtil.USER_ACTIVE);\r\n                regUser.setAccountDetails(accountDetails);\r\n                Registereduser userSaved = repoSaveUser(regUser);\r\n\r\n                return userSaved;\r\n            }\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public int updatePassword(String encodedPassword, Long userId) throws Exception {\r\n\r\n        return userRepo.updatePassword(encodedPassword, userId);\r\n\r\n    }\r\n\r\n    public int updatePasswordWithAuthType(String encodedPassword, Long userId, AuthProvider authProvider) throws Exception {\r\n\r\n        return userRepo.updatePasswordwithAuthType(encodedPassword, 1, userId);\r\n\r\n    }\r\n\r\n    public int updateUserDetails(ChangeUserDetailsDTO changeUserDetailsDTO, Long userId) throws Exception {\r\n\r\n        return userRepo.updateUserDetails(changeUserDetailsDTO.getFirstName(), changeUserDetailsDTO.getLastName(), changeUserDetailsDTO.getMobileNo(), userId);\r\n\r\n    }\r\n\r\n    public Map<String, Object> getUser() {\r\n        try {\r\n            UserPrincipal princple = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();\r\n\r\n            Optional<Registereduser> user = userRepo.findById(princple.getId());\r\n            Map<String, Object> map = new HashMap<>();\r\n\r\n\r\n            if (user.isPresent()) {\r\n                Registereduser temp = user.get();\r\n                map.put(\"userid\", temp.getUserid());\r\n                map.put(\"username\", temp.getUsername());\r\n                map.put(\"firstname\", temp.getFirstname());\r\n                map.put(\"lastname\", temp.getLastname());\r\n                map.put(\"mobileno\", temp.getMobileno());\r\n                map.put(\"email\", temp.getEmail());\r\n                map.put(\"createdOn\", temp.getCreatedOn());\r\n                map.put(\"roles\", temp.getRoles());\r\n\r\n\r\n            }\r\n\r\n            return map;\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n\r\n    public Optional<Registereduser> findByEmail(String email) {\r\n        try {\r\n            return userRepo.findByEmail(email);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public Optional<Registereduser> findById(Long userId) {\r\n        try {\r\n            return userRepo.findById(userId);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public List<Registereduser> getAllUser() {\r\n        try {\r\n\r\n            return userRepo.findAll();\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n\r\n    public Registereduser registerNewUser(OAuth2UserRequest oAuth2UserRequest, OAuth2UserInfo oAuth2UserInfo) {\r\n        try {\r\n            Registereduser user = new Registereduser();\r\n\r\n            user.setProvider(AuthProvider.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId()));\r\n            user.setProviderId(oAuth2UserInfo.getId());\r\n            user.setUsername(oAuth2UserInfo.getName());\r\n            user.setEmail(oAuth2UserInfo.getEmail());\r\n            user.setFirstname(oAuth2UserInfo.getName());\r\n            Calendar cal = Calendar.getInstance();\r\n            user.setCreatedOn(new Timestamp(cal.getTimeInMillis()));\r\n            user.setImageUrl(oAuth2UserInfo.getImageUrl());\r\n            Set<Roles> roleSet = new HashSet<>();\r\n            roleSet.add(rolesRepo.findByRolename(ConstantsUtil.USER_ROLE));\r\n            user.setRoles(roleSet);\r\n            AccountDetails accountDetails = new AccountDetails();\r\n            accountDetails.setIsPremiumAccount(ConstantsUtil.PREMIUM_ACCOUNT_ACTIVE_INACTIVE);\r\n            accountDetails.setIsDeleteScheduled(ConstantsUtil.DELETE_ACCOUNT_ACTIVE_INACTIVE);\r\n            accountDetails.setStorageUse(0L);\r\n            accountDetails.setIsActive(ConstantsUtil.USER_ACTIVE);\r\n            user.setAccountDetails(accountDetails);\r\n            return repoSaveUser(user);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public Registereduser updateExistingUser(Registereduser existingUser, OAuth2UserInfo oAuth2UserInfo) {\r\n        try {\r\n            existingUser.setUsername(oAuth2UserInfo.getName());\r\n            existingUser.setImageUrl(oAuth2UserInfo.getImageUrl());\r\n            return userRepo.save(existingUser);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public void updateAuthenticationType(String username, String oauth2ClientName) {\r\n        try {\r\n            AuthProvider authType = AuthProvider.valueOf(oauth2ClientName.toUpperCase());\r\n            userRepo.updateAuthenticationType(authType, username);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public void updateNotificationToken(String notificationToken) {\r\n        try {\r\n            UserPrincipal princple = (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();\r\n\r\n            userRepo.updateNotificationToken(notificationToken, princple.getId());\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public void updateRefreshToken(String refreshToken, Long userId) {\r\n        try {\r\n           // String token = UUID.randomUUID().toString();\r\n            PasswordResetToken myToken = passwordResetTokenRepo.findByToken(refreshToken).get();\r\n            Calendar cal = Calendar.getInstance();\r\n            cal.add(Calendar.MONTH, 6);\r\n            myToken.setToken(refreshToken);\r\n            Date date = new Date(cal.getTimeInMillis());\r\n            myToken.setExpiryDate(date);\r\n            passwordResetTokenRepo.save(myToken);\r\n            userRepo.updateRefreshToken(refreshToken, userId);\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n\r\n    public void createPasswordResetTokenForUser(Long userId, String token, boolean refreshToken) {\r\n        PasswordResetToken myToken = new PasswordResetToken();\r\n        Calendar cal = Calendar.getInstance();\r\n        if (refreshToken) {\r\n            cal.add(Calendar.MONTH, 6);\r\n        } else {\r\n            cal.add(Calendar.DATE, 2);\r\n        }\r\n\r\n        myToken.setToken(token);\r\n        myToken.setUserId(userId);\r\n        Date date = new Date(cal.getTimeInMillis());\r\n        myToken.setExpiryDate(date);\r\n        passwordResetTokenRepo.save(myToken);\r\n    }\r\n\r\n    public String validatePasswordResetToken(String token) {\r\n\r\n        Optional<PasswordResetToken> passToken = findByToken(token);\r\n        return passToken.isEmpty() ? \"invalidToken\"\r\n                : isTokenExpired(passToken.get()) ? \"expired\"\r\n                : null;\r\n    }\r\n\r\n    public Optional<PasswordResetToken> findByToken(String token) {\r\n        return passwordResetTokenRepo.findByToken(token);\r\n    }\r\n\r\n\r\n    private boolean isTokenExpired(PasswordResetToken passToken) {\r\n        final Calendar cal = Calendar.getInstance();\r\n        return passToken.getExpiryDate().before(cal.getTime());\r\n    }\r\n\r\n    public List<MarketingUserDTO> getAllUser(Integer hasCourse) {\r\n        try {\r\n            List<Registereduser> r = userRepo.findAllByMarketingUser(ConstantsUtil.USER_ROLE);\r\n            List<MarketingUserDTO> marketingUserDTOList = new ArrayList<>();\r\n            for (Registereduser user : r\r\n            ) {\r\n                MarketingUserDTO marketingUserDTO = new MarketingUserDTO();\r\n                String userName = user.getFirstname();\r\n                if (user.getLastname() != null) {\r\n                    userName = userName + \" \" + user.getLastname();\r\n                }\r\n                marketingUserDTO.setUserName(userName);\r\n                marketingUserDTO.setMobileNo(user.getMobileno());\r\n                marketingUserDTO.setCreatedOn(user.getCreatedOn());\r\n                marketingUserDTO.setEmailId(user.getEmail());\r\n                marketingUserDTO.setIsActive(user.getAccountDetails().getIsActive());\r\n\r\n//            if(hasCourse!=null) {\r\n//                if (hasCourse == 1) {\r\n//                    marketingUserDTO.setCourseList(purchaseCourseService.getCourseByPurchasedCourse(user.getUserid()));\r\n//                }\r\n//            }\r\n                marketingUserDTOList.add(marketingUserDTO);\r\n            }\r\n            return marketingUserDTOList;\r\n\r\n        } catch (Exception e) {\r\n\r\n            logger.error(e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    public Page<UserListDTO> getFilteredUsers(String role, String email, String sortBy, String sortDirection, int page, int size) {\r\n        try {\r\n            List<Registereduser> users = userRepo.findByRoleAndEmailContainingFetchAll(role, email);\r\n\r\n            List<UserListDTO> dtos = users.stream()\r\n                .map(UserListDTO::fromEntity)\r\n                .collect(Collectors.toList());\r\n\r\n            Comparator<UserListDTO> comparator = getUserListDTOComparator(sortBy, sortDirection);\r\n\r\n            dtos.sort(comparator);\r\n\r\n            int fromIndex = page * size;\r\n            int toIndex = Math.min(fromIndex + size, dtos.size());\r\n\r\n            List<UserListDTO> pagedDtos = fromIndex < toIndex\r\n                ? dtos.subList(fromIndex, toIndex)\r\n                : new ArrayList<>();\r\n\r\n            return new PageImpl<>(pagedDtos, PageRequest.of(page, size), dtos.size());\r\n        } catch (Exception e) {\r\n            logger.error(\"Error filtering users: \" + e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    private static Comparator<UserListDTO> getUserListDTOComparator(String sortBy, String sortDirection) {\r\n        Comparator<UserListDTO> comparator;\r\n        if (sortBy.equalsIgnoreCase(\"firstName\") || sortBy.equalsIgnoreCase(\"firstname\")) {\r\n            comparator = Comparator.comparing(UserListDTO::getFirstname,\r\n                Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER));\r\n        } else if (sortBy.equalsIgnoreCase(\"lastName\") || sortBy.equalsIgnoreCase(\"lastname\")) {\r\n            comparator = Comparator.comparing(UserListDTO::getLastname,\r\n                Comparator.nullsLast(String.CASE_INSENSITIVE_ORDER));\r\n        } else {\r\n            comparator = Comparator.comparing(UserListDTO::getUserid);\r\n        }\r\n\r\n        if (sortDirection.equalsIgnoreCase(\"desc\")) {\r\n            comparator = comparator.reversed();\r\n        }\r\n        return comparator;\r\n    }\r\n\r\n    public List<UserListDTO> getAllUsersAsDTO() {\r\n        try {\r\n            List<Registereduser> users = userRepo.findAll();\r\n            return users.stream()\r\n                .map(UserListDTO::fromEntity)\r\n                .collect(Collectors.toList());\r\n        } catch (Exception e) {\r\n            logger.error(\"Error getting all users as DTOs: \" + e.getMessage());\r\n            throw e;\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/job/jobportal/service/UserService.java b/src/main/java/com/job/jobportal/service/UserService.java
--- a/src/main/java/com/job/jobportal/service/UserService.java	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/java/com/job/jobportal/service/UserService.java	(date *************)
@@ -7,6 +7,7 @@
 import com.job.jobportal.model.AccountDetails;
 import com.job.jobportal.model.Registereduser;
 import com.job.jobportal.model.Roles;
+import com.job.jobportal.model.Subscriber;
 import com.job.jobportal.repository.AccountDetailsRepo;
 import com.job.jobportal.repository.PasswordResetTokenRepo;
 import com.job.jobportal.repository.RegisteruserRepository;
@@ -54,6 +55,8 @@
     EmailService emailService;
     @Autowired
     private RolesRepository rolesRepo;
+    @Autowired
+    private SubscriberService subscriberService;
     @Value("${application.email}")
     private String email;
 
@@ -323,35 +326,56 @@
         return passToken.getExpiryDate().before(cal.getTime());
     }
 
-    public List<MarketingUserDTO> getAllUser(Integer hasCourse) {
+    public Page<MarketingUserDTO> getAllUser(Integer hasCourse, String userType, Pageable pageable) {
         try {
-            List<Registereduser> r = userRepo.findAllByMarketingUser(ConstantsUtil.USER_ROLE);
-            List<MarketingUserDTO> marketingUserDTOList = new ArrayList<>();
-            for (Registereduser user : r
-            ) {
-                MarketingUserDTO marketingUserDTO = new MarketingUserDTO();
-                String userName = user.getFirstname();
-                if (user.getLastname() != null) {
-                    userName = userName + " " + user.getLastname();
-                }
-                marketingUserDTO.setUserName(userName);
-                marketingUserDTO.setMobileNo(user.getMobileno());
-                marketingUserDTO.setCreatedOn(user.getCreatedOn());
-                marketingUserDTO.setEmailId(user.getEmail());
-                marketingUserDTO.setIsActive(user.getAccountDetails().getIsActive());
+            List<MarketingUserDTO> allUsers = new ArrayList<>();
+
+            if (userType == null || userType.equalsIgnoreCase("USER")) {
+                List<Registereduser> users = userRepo.findAllByMarketingUser(ConstantsUtil.USER_ROLE);
+                for (Registereduser user : users) {
+                    MarketingUserDTO dto = new MarketingUserDTO();
+                    String userName = user.getFirstname();
+                    if (user.getLastname() != null) {
+                        userName = userName + " " + user.getLastname();
+                    }
+                    dto.setUserName(userName);
+                    dto.setMobileNo(user.getMobileno());
+                    dto.setCreatedOn(user.getCreatedOn());
+                    dto.setEmailId(user.getEmail());
+                    dto.setIsActive(user.getAccountDetails().getIsActive());
+                    dto.setUserType("USER");
 
 //            if(hasCourse!=null) {
 //                if (hasCourse == 1) {
 //                    marketingUserDTO.setCourseList(purchaseCourseService.getCourseByPurchasedCourse(user.getUserid()));
 //                }
 //            }
-                marketingUserDTOList.add(marketingUserDTO);
+                    allUsers.add(dto);
+                }
+            }
+
+            if (userType == null || userType.equalsIgnoreCase("SUBSCRIBER")) {
+                List<Subscriber> subscribers = subscriberService.getActiveSubscribers();
+                for (Subscriber subscriber : subscribers) {
+                    MarketingUserDTO dto = new MarketingUserDTO(
+                        subscriber.getEmail(),
+                        subscriber.getCreatedAt(),
+                        "SUBSCRIBER"
+                    );
+                    dto.setUserName("Subscriber");
+                    allUsers.add(dto);
+                }
             }
-            return marketingUserDTOList;
 
+            allUsers.sort((a, b) -> b.getCreatedOn().compareTo(a.getCreatedOn()));
+
+            int start = (int) pageable.getOffset();
+            int end = Math.min(start + pageable.getPageSize(), allUsers.size());
+            List<MarketingUserDTO> pageContent = start < end ? allUsers.subList(start, end) : new ArrayList<>();
+
+            return new PageImpl<>(pageContent, pageable, allUsers.size());
         } catch (Exception e) {
-
-            logger.error(e.getMessage());
+            logger.error("Error in getAllUser: " + e.getMessage());
             throw e;
         }
     }
Index: src/main/java/com/job/jobportal/dto/MarketingUserDTO.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.job.jobportal.dto;\r\n\r\nimport lombok.Data;\r\n\r\nimport java.sql.Timestamp;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class MarketingUserDTO {\r\n\r\n    private String userName;\r\n\r\n    private String mobileNo;\r\n\r\n\r\n    private String emailId;\r\n\r\n    private Timestamp createdOn;\r\n\r\n    private int isActive;\r\n\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/job/jobportal/dto/MarketingUserDTO.java b/src/main/java/com/job/jobportal/dto/MarketingUserDTO.java
--- a/src/main/java/com/job/jobportal/dto/MarketingUserDTO.java	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/java/com/job/jobportal/dto/MarketingUserDTO.java	(date 1748495975729)
@@ -12,12 +12,22 @@
 
     private String mobileNo;
 
-
     private String emailId;
 
     private Timestamp createdOn;
 
     private int isActive;
 
+    private String userType; // "USER" or "SUBSCRIBER"
 
+    public MarketingUserDTO() {
+        this.userType = "USER";
+    }
+
+    public MarketingUserDTO(String emailId, Timestamp createdOn, String userType) {
+        this.emailId = emailId;
+        this.createdOn = createdOn;
+        this.userType = userType;
+        this.isActive = 1;
+    }
 }
Index: src/main/resources/application-qa.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>spring.jpa.hibernate.ddl-auto=update\r\n#spring.jpa.hibernate.ddl-auto=create-drop\r\n#spring.jpa.hibernate.ddl-auto=create\r\n#spring.jpa.hibernate.ddl-auto=none\r\nspring.datasource.url=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# application.baseFrontendUrl=http://app.localhost:3000\r\napplication.baseFrontendUrl=http://localhost:3000\r\n\r\nmanagement.endpoints.web.exposure.include=health,info,metrics\r\n# Optionally, configure access to the health endpoint\r\nmanagement.endpoint.health.probe.enabled=true\r\nmanagement.endpoints.web.base-path=/actuator\r\nmanagement.endpoint.health.show-details=always\r\napplication.ticketEmail=<EMAIL>\r\napplication.certificatecode=JB\r\napplication.name=Job Portal\r\napplication.description=Portal for Applying Jobs under several department\r\napplication.version=1.0.0\r\napp.base-url=https://qabranchaws.d261u2ehm8xooe.amplifyapp.com/\r\nserver.port=8080\r\nspring.sql.init.mode=embedded\r\n#spring.sql.init.mode=never\r\napp.database.initialize=true\r\nspring.jpa.open-in-view=false\r\nspring.jpa.defer-datasource-initialization=true\r\n#spring.flyway.enabled=true\r\n#spring.jpa.defer-datasource-initialization=false\r\nspring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER\r\nspring.servlet.multipart.max-file-size=-1\r\nspring.servlet.multipart.max-request-size=-1\r\napplication.aws.bucketname=ebrainyvideostreaming\r\napplication.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com\r\napplication.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net\r\napplication.aws.accessKey=********************\r\napplication.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM\r\napplication.aws.region=eu-north-1\r\napplication.aws.secretName=stripe\r\n\r\napplication.email=<EMAIL>\r\n#email.domain.from=<EMAIL>\r\n\r\n# Social media links\r\nsocial.facebook.url=https://www.facebook.com/groglojobs\r\nsocial.linkedin.url=https://www.linkedin.com/company/groglojobs\r\nsocial.twitter.url=https://twitter.com/groglojobs\r\n\r\n# Social media icons\r\nsocial.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png\r\nsocial.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png\r\nsocial.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png\r\n\r\n\r\n#mail\r\nemail.provider=domain\r\n\r\n# gmail\r\nspring.mail.host=smtp.gmail.com\r\nspring.mail.port=587\r\nspring.mail.username=<EMAIL>\r\nspring.mail.password=aoamtqqqspnwjuvu\r\n#domain\r\n#email.domain.host=smtp.zoho.in\r\n#email.domain.port=465\r\n#email.domain.username=<EMAIL>\r\n#email.domain.password=rfxVXje1NXUz\r\n#email.domain.from=<EMAIL>\r\nemail.domain.host=smtp.zoho.eu\r\nemail.domain.port=465\r\nemail.domain.username=<EMAIL>\r\nemail.domain.password=vNsnxLakWUnr\r\nemail.domain.from=<EMAIL>\r\n\r\n\r\n\r\nspring.mail.properties.mail.smtp.auth=true\r\nspring.mail.properties.mail.smtp.starttls.enable=true\r\n\r\n#sms\r\nsms.PHONE_NUMBER=+16203901757\r\n#payment\r\n# Razorpay\r\nstripe.webhook.signing.currency=GBP\r\napplication.multiCurrency=false\r\napplication.multiCurrencyList=USD,GBP,EUR\r\n#security\r\nspring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com\r\nspring.security.oauth2.client.registration.google.clientSecret=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\r\nspring.security.oauth2.client.registration.google.scope=email, profile\r\nspring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.registration.facebook.clientId=1456102035268496\r\nspring.security.oauth2.client.registration.facebook.clientSecret=********************************\r\nspring.security.oauth2.client.registration.facebook.scope=email, public_profile\r\nspring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth\r\nspring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token\r\n#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified\r\napp.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1\r\napp.auth.tokenExpirationMsec=864000000\r\napp.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*\r\napp.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect\r\n#logs\r\nlogging.level.root=INFO\r\nlogging.config=classpath:logback-spring.xml\r\nlogging.level.org.springframework=INFO\r\nlogging.level.org.springframework.boot=INFO\r\nlogging.level.org.springframework.boot.autoconfigure=INFO\r\nlogging.level.org.springframework.boot.context=INFO\r\nlogging.level.org.springframework.boot.devtools=INFO\r\nlogging.level.org.springframework.web=INFO\r\nspring.devtools.restart.enabled=false\r\n\r\n# stripe\r\nstripe.api.key=sk_test_51ROBqQPImHquQ8udrDgxqFEuPug1jrX4DUYyUckEyNIyXXeGK7C7EI2wMYhVGzTYcYcB4HeQpYxBIQQq92C6jwHq00pCJcTPb3\r\nstripe.webhook.signing.secret=whsec_6oYd58ONxjwojIWcHp3bLAD81b2pIDYf\r\nstripe.price.standard.monthly=price_1RODXkPImHquQ8udb9ZtN7cR\r\nstripe.price.standard.yearly=price_1RODpmPImHquQ8udnYE8iHHz\r\nstripe.price.premium.monthly=price_1RODaWPImHquQ8udXW3EoAkW\r\nstripe.price.premium.yearly=price_1RODqYPImHquQ8udiCh7r6eX\r\nstripe.price.enterprise.monthly=price_1RODcFPImHquQ8udokaJ8Wl8\r\nstripe.price.enterprise.yearly=price_1RODrbPImHquQ8uds6NiF1wj\r\n\r\n\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/src/main/resources/application-qa.properties b/src/main/resources/application-qa.properties
--- a/src/main/resources/application-qa.properties	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/resources/application-qa.properties	(date 1748496188169)
@@ -24,7 +24,8 @@
 application.name=Job Portal
 application.description=Portal for Applying Jobs under several department
 application.version=1.0.0
-app.base-url=https://qabranchaws.d261u2ehm8xooe.amplifyapp.com/
+#app.base-url=https://qabranchaws.d261u2ehm8xooe.amplifyapp.com/
+app.base-url=http://localhost:3000
 server.port=8080
 spring.sql.init.mode=embedded
 #spring.sql.init.mode=never
Index: src/main/java/com/job/jobportal/controller/UserController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.job.jobportal.controller;\r\n\r\n\r\nimport com.job.jobportal.dto.MarketingUserDTO;\r\nimport com.job.jobportal.dto.NotificationDTO;\r\nimport com.job.jobportal.dto.SurveyDTO;\r\nimport com.job.jobportal.dto.UserDTO;\r\nimport com.job.jobportal.response.ApiResponse;\r\nimport com.job.jobportal.response.BadRequestException;\r\nimport com.job.jobportal.service.SurveyService;\r\nimport com.job.jobportal.service.UserService;\r\nimport jakarta.validation.Valid;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.context.MessageSource;\r\nimport org.springframework.context.i18n.LocaleContextHolder;\r\nimport org.springframework.http.HttpStatus;\r\nimport org.springframework.http.ResponseEntity;\r\nimport org.springframework.web.bind.annotation.*;\r\n\r\nimport java.util.List;\r\n\r\n\r\n@RestController\r\n@RequestMapping(\"/\")\r\npublic class UserController {\r\n\r\n    @Autowired\r\n    MessageSource message;\r\n\r\n    @Autowired\r\n    UserService userService;\r\n\r\n    @Autowired\r\n    SurveyService surveyService;\r\n\r\n    private static final Logger logger = LoggerFactory.getLogger(UserController.class);\r\n\r\n    @GetMapping(\"/users\")\r\n    public ResponseEntity<?> getAllUser(@RequestParam(required = false) Integer hasCourse) {\r\n        List<MarketingUserDTO> marketingUserDTOS = userService.getAllUser(hasCourse);\r\n        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserDTOS, message.getMessage(\"msg.userdetails_get_success\", null, LocaleContextHolder.getLocale())), HttpStatus.OK);\r\n    }\r\n\r\n\r\n    @PostMapping(\"user\")\r\n    public ResponseEntity<?> addUser(@RequestBody @Valid UserDTO user) throws Exception {\r\n        try {\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, userService.saveUser(user),\r\n                    message.getMessage(\"msg.user_created_success\", null, LocaleContextHolder.getLocale())), HttpStatus.OK);\r\n        } catch (BadRequestException e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);\r\n\r\n        } catch (Exception e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n//\t@GetMapping()\r\n//\tpublic ResponseEntity<?> currentUser(@RegisteredOAuth2AuthorizedClient(\"google\") OAuth2AuthorizedClient user) throws Exception {\r\n//\t\ttry {\r\n//\t\t\tuserService.updateRefreshToken( user.getRefreshToken().getTokenValue(),user.getPrincipalName());\r\n//\t\t\treturn new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, user,\r\n//\t\t\t\t\tmessage.getMessage(\"msg.user_created_success\", null, LocaleContextHolder.getLocale())),HttpStatus.OK);\r\n//\t\t}\r\n//\r\n//\t\tcatch (Exception e) {\r\n//\t\t\treturn new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);\r\n//\t\t}\r\n//\t}\r\n\r\n    @GetMapping(\"user\")\r\n    public ResponseEntity<?> getUser() throws Exception {\r\n        try {\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, userService.getUser(),\r\n                    message.getMessage(\"msg.user_created_success\", null, LocaleContextHolder.getLocale())), HttpStatus.OK);\r\n        } catch (BadRequestException e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);\r\n\r\n        } catch (Exception e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n\r\n    @GetMapping(\"alluser\")\r\n    public ResponseEntity<?> getAllUser(\r\n            @RequestParam(required = false) String role,\r\n            @RequestParam(required = false) String email,\r\n            @RequestParam(defaultValue = \"firstName\") String sortBy,\r\n            @RequestParam(defaultValue = \"asc\") String sortDirection,\r\n            @RequestParam(defaultValue = \"0\") int page,\r\n            @RequestParam(defaultValue = \"10\") int size) throws Exception {\r\n        try {\r\n            if (role == null && email == null &&\r\n                sortBy.equals(\"firstName\") && sortDirection.equals(\"asc\") &&\r\n                page == 0 && size == 10) {\r\n                return new ResponseEntity<>(new ApiResponse<>(\r\n                    HttpStatus.OK,\r\n                    true,\r\n                    userService.getAllUsersAsDTO(),\r\n                    message.getMessage(\"msg.userdetails_get_success\", null, LocaleContextHolder.getLocale())),\r\n                    HttpStatus.OK);\r\n            }\r\n\r\n            return new ResponseEntity<>(new ApiResponse<>(\r\n                HttpStatus.OK,\r\n                true,\r\n                userService.getFilteredUsers(role, email, sortBy, sortDirection, page, size),\r\n                message.getMessage(\"msg.userdetails_get_success\", null, LocaleContextHolder.getLocale())),\r\n                HttpStatus.OK);\r\n        } catch (BadRequestException e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);\r\n\r\n        } catch (Exception e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n\r\n    @PostMapping(\"user/survey\")\r\n    public ResponseEntity<?> updateSurvey(@RequestBody SurveyDTO surveyDTO) {\r\n        try {\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, surveyService.updateSurvey(surveyDTO),\r\n                    message.getMessage(\"msg.user_created_success\", null, LocaleContextHolder.getLocale())), HttpStatus.OK);\r\n        } catch (BadRequestException e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);\r\n\r\n        } catch (Exception e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @PutMapping(\"notificationToken\")\r\n    public ResponseEntity<?> addUser(@RequestBody NotificationDTO notificationDTO) throws Exception {\r\n        try {\r\n            userService.updateNotificationToken(notificationDTO.getToken());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, null,\r\n                    message.getMessage(\"msg.user_created_success\", null, LocaleContextHolder.getLocale())), HttpStatus.OK);\r\n        } catch (BadRequestException e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);\r\n\r\n        } catch (Exception e) {\r\n            logger.error(e.getMessage());\r\n            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/job/jobportal/controller/UserController.java b/src/main/java/com/job/jobportal/controller/UserController.java
--- a/src/main/java/com/job/jobportal/controller/UserController.java	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/java/com/job/jobportal/controller/UserController.java	(date 1748495975718)
@@ -3,10 +3,12 @@
 
 import com.job.jobportal.dto.MarketingUserDTO;
 import com.job.jobportal.dto.NotificationDTO;
+import com.job.jobportal.dto.SubscriberDTO;
 import com.job.jobportal.dto.SurveyDTO;
 import com.job.jobportal.dto.UserDTO;
 import com.job.jobportal.response.ApiResponse;
 import com.job.jobportal.response.BadRequestException;
+import com.job.jobportal.service.SubscriberService;
 import com.job.jobportal.service.SurveyService;
 import com.job.jobportal.service.UserService;
 import jakarta.validation.Valid;
@@ -15,12 +17,13 @@
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.context.MessageSource;
 import org.springframework.context.i18n.LocaleContextHolder;
+import org.springframework.data.domain.Page;
+import org.springframework.data.domain.PageRequest;
+import org.springframework.data.domain.Pageable;
 import org.springframework.http.HttpStatus;
 import org.springframework.http.ResponseEntity;
 import org.springframework.web.bind.annotation.*;
 
-import java.util.List;
-
 
 @RestController
 @RequestMapping("/")
@@ -35,12 +38,22 @@
     @Autowired
     SurveyService surveyService;
 
+    @Autowired
+    SubscriberService subscriberService;
+
     private static final Logger logger = LoggerFactory.getLogger(UserController.class);
 
     @GetMapping("/users")
-    public ResponseEntity<?> getAllUser(@RequestParam(required = false) Integer hasCourse) {
-        List<MarketingUserDTO> marketingUserDTOS = userService.getAllUser(hasCourse);
-        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserDTOS, message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
+    public ResponseEntity<?> getAllUser(
+            @RequestParam(required = false) Integer hasCourse,
+            @RequestParam(required = false) String userType,
+            @RequestParam(defaultValue = "0") int page,
+            @RequestParam(defaultValue = "10") int size) {
+
+        Pageable pageable = PageRequest.of(page, size);
+        Page<MarketingUserDTO> marketingUserPage = userService.getAllUser(hasCourse, userType, pageable);
+        return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, marketingUserPage,
+            message.getMessage("msg.userdetails_get_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
     }
 
 
@@ -149,6 +162,21 @@
         } catch (BadRequestException e) {
             logger.error(e.getMessage());
             return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
+
+        } catch (Exception e) {
+            logger.error(e.getMessage());
+            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, null, e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
+        }
+    }
+
+    @PostMapping("users/subscribers")
+    public ResponseEntity<?> addSubscriber(@RequestBody @Valid SubscriberDTO subscriberDTO) throws Exception {
+        try {
+            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.OK, true, subscriberService.saveSubscriber(subscriberDTO),
+                    message.getMessage("msg.user_created_success", null, LocaleContextHolder.getLocale())), HttpStatus.OK);
+        } catch (BadRequestException e) {
+            logger.error(e.getMessage());
+            return new ResponseEntity<>(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, e.getMessage()), HttpStatus.BAD_REQUEST);
 
         } catch (Exception e) {
             logger.error(e.getMessage());
Index: src/main/java/com/job/jobportal/repository/RegisteruserRepository.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.job.jobportal.repository;\r\n\r\nimport com.job.jobportal.model.AccountDetails;\r\nimport com.job.jobportal.model.CompanyProfile;\r\nimport com.job.jobportal.model.Registereduser;\r\nimport com.job.jobportal.security.AuthProvider;\r\nimport jakarta.transaction.Transactional;\r\nimport org.springframework.data.domain.Page;\r\nimport org.springframework.data.domain.Pageable;\r\nimport org.springframework.data.jpa.repository.JpaRepository;\r\nimport org.springframework.data.jpa.repository.Modifying;\r\nimport org.springframework.data.jpa.repository.Query;\r\nimport org.springframework.data.repository.query.Param;\r\nimport org.springframework.stereotype.Repository;\r\n\r\nimport java.util.List;\r\nimport java.util.Optional;\r\n\r\n\r\n@Repository\r\n@Transactional\r\npublic interface RegisteruserRepository extends JpaRepository<Registereduser, Long> {\r\n\r\n    Optional<Registereduser> findByEmail(String emailId);\r\n\r\n    Registereduser findByUsername(String username);\r\n\r\n\r\n    Boolean existsByEmail(String email);\r\n\r\n    @Query(\"select r from Registereduser  r inner join r.roles u  where u.rolename=:rolename\")\r\n    List<Registereduser> findAllByMarketingUser(@Param(\"rolename\") String rolename);\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.refreshToken =:refreshToken WHERE u.userid =:userid\")\r\n    void updateRefreshToken(@Param(\"refreshToken\") String refreshToken, @Param(\"userid\") Long userid);\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.notificationToken =:notificationToken WHERE u.userid =:userid\")\r\n    void updateNotificationToken(@Param(\"notificationToken\") String notificationToken, @Param(\"userid\") Long userid);\r\n\r\n    @Query(\"select r from Registereduser  r  where r.notificationToken IS NOT NULL AND r.notificationToken <> ''\")\r\n    List<Registereduser> findAllByNotificationToken();\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.provider =:provider WHERE u.username =:username\")\r\n    void updateAuthenticationType(@Param(\"provider\") AuthProvider authType, @Param(\"username\") String username);\r\n\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.password =:password  WHERE u.userid =:userid\")\r\n    int updatePassword(@Param(\"password\") String password, @Param(\"userid\") Long userid);\r\n\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.password =:password ,u.hasPassword=:hasPassword WHERE u.userid =:userid\")\r\n    int updatePasswordwithAuthType(@Param(\"password\") String password, @Param(\"hasPassword\") int hasPassword, @Param(\"userid\") Long userid);\r\n\r\n\r\n    @Modifying\r\n    @Query(\"UPDATE Registereduser u SET u.firstname =:firstname , u.lastname=:lastname ,u.mobileno =:mobileno WHERE u.userid =:userid\")\r\n    int updateUserDetails(@Param(\"firstname\") String firstname, @Param(\"lastname\") String lastname, @Param(\"mobileno\") String mobileno, @Param(\"userid\") Long userid);\r\n\r\n    Optional<Registereduser> findByAccountDetails(AccountDetails accountDetails);\r\n\r\n    @Query(\"SELECT u FROM Registereduser u LEFT JOIN FETCH u.companyProfile WHERE u.userid = :userid\")\r\n    Optional<Registereduser> findCompanyProfileByUserId(@Param(\"userid\")Long userId);\r\n\r\n    boolean existsByMobileno(String mobileNumber);\r\n\r\n    Registereduser findByMobileno(String mobileNumber);\r\n\r\n    @Query(\"SELECT DISTINCT r FROM Registereduser r \" +\r\n           \"LEFT JOIN FETCH r.roles role \" +\r\n           \"LEFT JOIN FETCH r.accountDetails \" +\r\n           \"WHERE (:role IS NULL OR role.rolename = :role) \" +\r\n           \"AND (:email IS NULL OR LOWER(r.email) LIKE LOWER(CONCAT('%', :email, '%')))\")\r\n    List<Registereduser> findByRoleAndEmailContainingFetchAll(\r\n            @Param(\"role\") String role,\r\n            @Param(\"email\") String email);\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/job/jobportal/repository/RegisteruserRepository.java b/src/main/java/com/job/jobportal/repository/RegisteruserRepository.java
--- a/src/main/java/com/job/jobportal/repository/RegisteruserRepository.java	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/src/main/java/com/job/jobportal/repository/RegisteruserRepository.java	(date *************)
@@ -1,5 +1,6 @@
 package com.job.jobportal.repository;
 
+import com.job.jobportal.dto.MarketingUserDTO;
 import com.job.jobportal.model.AccountDetails;
 import com.job.jobportal.model.CompanyProfile;
 import com.job.jobportal.model.Registereduser;
@@ -31,6 +32,11 @@
     @Query("select r from Registereduser  r inner join r.roles u  where u.rolename=:rolename")
     List<Registereduser> findAllByMarketingUser(@Param("rolename") String rolename);
 
+    @Query("select r from Registereduser r inner join r.roles u where u.rolename=:rolename order by r.createdOn desc")
+    Page<Registereduser> findAllByMarketingUserPaginated(@Param("rolename") String rolename, Pageable pageable);
+
+
+
     @Modifying
     @Query("UPDATE Registereduser u SET u.refreshToken =:refreshToken WHERE u.userid =:userid")
     void updateRefreshToken(@Param("refreshToken") String refreshToken, @Param("userid") Long userid);
Index: target/classes/application-dev.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>#spring.jpa.hibernate.ddl-auto=update\r\n#spring.jpa.hibernate.ddl-auto=create-drop\r\nspring.jpa.hibernate.ddl-auto=create\r\n#spring.jpa.hibernate.ddl-auto=none\r\nspring.datasource.url=*************************************\r\nspring.datasource.username=root\r\nspring.datasource.password=system123#\r\nspring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver\r\npring.jpa.database-platform=org.hibernate.dialect.MySQLDialect\r\nspring.jpa.show-sql=true\r\njwt.secret=jobportalapp\r\nspring.jpa.properties.hibernate.jdbc.time_zone=UTC\r\napplication.baseUrl=http://localhost:8080\r\napplication.baseFrontendUrl=http://app.localhost:3000\r\napplication.email=<EMAIL>\r\napplication.ticketEmail=<EMAIL>\r\napplication.certificatecode=JB\r\napplication.name=Job Portal\r\napplication.description=Portal for Applying Jobs under several department\r\napplication.version=1.0.0\r\nserver.port=8080\r\nspring.sql.init.mode=embedded\r\n#spring.sql.init.mode=never\r\napp.database.initialize=true\r\nspring.jpa.defer-datasource-initialization=true\r\n#spring.flyway.enabled=true\r\n#spring.jpa.defer-datasource-initialization=false\r\nspring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER\r\nspring.servlet.multipart.max-file-size=-1\r\nspring.servlet.multipart.max-request-size=-1\r\napplication.aws.bucketname=ebrainyvideostreamingtestss\r\napplication.aws.accessKey=********************\r\napplication.aws.secretKey=u6Fy39Cx1BMdVAwXxfWr9j2YKmcGDksgLsDqJXQd\r\napplication.aws.region=eu-north-1\r\napplication.aws.secretName=stripe\r\n#mail\r\nspring.mail.host=smtp.gmail.com\r\nspring.mail.port=587\r\nspring.mail.username=<EMAIL>\r\nspring.mail.password=aoamtqqqspnwjuvu\r\nspring.mail.properties.mail.smtp.auth=true\r\nspring.mail.properties.mail.smtp.starttls.enable=true\r\n#sms\r\nsms.PHONE_NUMBER=+16203901757\r\n#payment\r\n# Razorpay\r\nstripe.webhook.signing.currency=GBP\r\napplication.multiCurrency=false\r\napplication.multiCurrencyList=USD,GBP,EUR\r\n#security\r\nspring.security.oauth2.client.registration.google.clientId=10890670190-6pmq4d6q07fmf9cvcm03ktnod290oi32.apps.googleusercontent.com\r\nspring.security.oauth2.client.registration.google.clientSecret=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\r\nspring.security.oauth2.client.registration.google.scope=email, profile\r\nspring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.registration.facebook.clientId=1456102035268496\r\nspring.security.oauth2.client.registration.facebook.clientSecret=********************************\r\nspring.security.oauth2.client.registration.facebook.scope=email, public_profile\r\nspring.security.oauth2.client.registration.facebook.redirectUri={baseUrl}/oauth2/callback/{registrationId}\r\nspring.security.oauth2.client.provider.facebook.authorizationUri=https://www.facebook.com/v3.0/dialog/oauth\r\nspring.security.oauth2.client.provider.facebook.tokenUri=https://graph.facebook.com/v3.0/oauth/access_token\r\n#spring.security.oauth2.client.provider.facebook.userInfoUri=https://graph.facebook.com/v3.0/me?fields=id,first_name,middle_name,last_name,name,email,verified,is_verified\r\napp.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1\r\napp.auth.tokenExpirationMsec=864000000\r\napp.cors.allowedOrigins=http://localhost:3000,http://localhost:8080,http://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,https://jobportalqa-env.eba-9tpk4bzq.eu-north-1.elasticbeanstalk.com,*\r\napp.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect\r\n#logs\r\nlogging.level.root=INFO\r\nlogging.config=classpath:logback-spring.xml\r\nlogging.level.org.springframework=INFO\r\nlogging.level.org.springframework.boot=INFO\r\nlogging.level.org.springframework.boot.autoconfigure=INFO\r\nlogging.level.org.springframework.boot.context=INFO\r\nlogging.level.org.springframework.boot.devtools=INFO\r\nlogging.level.org.springframework.web=INFO\r\nspring.devtools.restart.enabled=false\r\n\r\n\r\n\r\n\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/target/classes/application-dev.properties b/target/classes/application-dev.properties
--- a/target/classes/application-dev.properties	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/target/classes/application-dev.properties	(date 1748496647650)
@@ -1,45 +1,87 @@
-#spring.jpa.hibernate.ddl-auto=update
+spring.jpa.hibernate.ddl-auto=update
 #spring.jpa.hibernate.ddl-auto=create-drop
-spring.jpa.hibernate.ddl-auto=create
+#spring.jpa.hibernate.ddl-auto=create
 #spring.jpa.hibernate.ddl-auto=none
 spring.datasource.url=*************************************
 spring.datasource.username=root
-spring.datasource.password=system123#
+spring.datasource.password=root
 spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
-pring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
+#spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
 spring.jpa.show-sql=true
 jwt.secret=jobportalapp
 spring.jpa.properties.hibernate.jdbc.time_zone=UTC
 application.baseUrl=http://localhost:8080
-application.baseFrontendUrl=http://app.localhost:3000
-application.email=<EMAIL>
+# application.baseFrontendUrl=http://app.localhost:3000
+application.baseFrontendUrl=http://localhost:3000
+management.endpoints.web.exposure.include=health,info,metrics
+# Optionally, configure access to the health endpoint
+management.endpoint.health.probe.enabled=true
+management.endpoints.web.base-path=/actuator
+management.endpoint.health.show-details=always
 application.ticketEmail=<EMAIL>
 application.certificatecode=JB
 application.name=Job Portal
 application.description=Portal for Applying Jobs under several department
 application.version=1.0.0
+app.base-url=https://www.groglojobs.co.uk
 server.port=8080
 spring.sql.init.mode=embedded
 #spring.sql.init.mode=never
 app.database.initialize=true
+spring.jpa.open-in-view=false
 spring.jpa.defer-datasource-initialization=true
 #spring.flyway.enabled=true
 #spring.jpa.defer-datasource-initialization=false
 spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
 spring.servlet.multipart.max-file-size=-1
 spring.servlet.multipart.max-request-size=-1
-application.aws.bucketname=ebrainyvideostreamingtestss
-application.aws.accessKey=********************
-application.aws.secretKey=u6Fy39Cx1BMdVAwXxfWr9j2YKmcGDksgLsDqJXQd
+application.aws.bucketname=ebrainyvideostreaming
+application.aws.import_excel=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com
+application.aws.cloudfronts3url=https://d19w1vowz8zr6e.cloudfront.net
+application.aws.accessKey=********************
+application.aws.secretKey=70ocLBJPVsJaNYEAwu3Pih1Dl3his8/lwztR5qYM
 application.aws.region=eu-north-1
 application.aws.secretName=stripe
+
+application.email=<EMAIL>
+#email.domain.from=<EMAIL>
+
+# Social media links
+social.facebook.url=https://www.facebook.com/groglojobs
+social.linkedin.url=https://www.linkedin.com/company/groglojobs
+social.twitter.url=https://twitter.com/groglojobs
+
+# Social media icons
+social.icon.facebook=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/facebook.png
+social.icon.linkedin=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/linkedin.png
+social.icon.twitter=https://ebrainyvideostreaming.s3.eu-north-1.amazonaws.com/social/twitter.png
+
+
 #mail
+email.provider=domain
+
+# gmail
 spring.mail.host=smtp.gmail.com
 spring.mail.port=587
 spring.mail.username=<EMAIL>
 spring.mail.password=aoamtqqqspnwjuvu
+#domain
+#email.domain.host=smtp.zoho.in
+#email.domain.port=465
+#email.domain.username=<EMAIL>
+#email.domain.password=rfxVXje1NXUz
+#email.domain.from=<EMAIL>
+email.domain.host=smtp.zoho.eu
+email.domain.port=465
+email.domain.username=<EMAIL>
+email.domain.password=vNsnxLakWUnr
+email.domain.from=<EMAIL>
+
+
+
 spring.mail.properties.mail.smtp.auth=true
 spring.mail.properties.mail.smtp.starttls.enable=true
+
 #sms
 sms.PHONE_NUMBER=+16203901757
 #payment
@@ -74,8 +116,15 @@
 logging.level.org.springframework.web=INFO
 spring.devtools.restart.enabled=false
 
-
-
+# stripe
+stripe.api.key=sk_test_51R6oOD4YEQBprOqE5njXOiLJbv3yOi3XZUVKOgMUpeSFBxG1BB6h9DMUR3QRJYkCKisdksd1UmAxEp5Y5OVNKTG1002yeP6bV1
+stripe.webhook.signing.secret=whsec_3e18ca3d7014104d8b29a889e2ead2513989296abded91963fd6ae676d931ba6
+stripe.price.standard.monthly=price_1R78mJ4YEQBprOqE5tTXs86x
+stripe.price.standard.yearly=price_standard_yearly_id
+stripe.price.premium.monthly=price_1R78mw4YEQBprOqEDP69lLFW
+stripe.price.premium.yearly=price_premium_yearly_id
+stripe.price.enterprise.monthly=price_enterprise_monthly_id
+stripe.price.enterprise.yearly=price_enterprise_yearly_id
 
 
 
Index: target/classes/schema.sql
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>INSERT  INTO roles(roleName) VALUES('ADMIN'),('MANAGER'),('STAFF'),('RECRUITER'),('CANDIDATE')\r\n$$\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/target/classes/schema.sql b/target/classes/schema.sql
--- a/target/classes/schema.sql	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/target/classes/schema.sql	(date *************)
@@ -1,2 +1,985 @@
-INSERT  INTO roles(roleName) VALUES('ADMIN'),('MANAGER'),('STAFF'),('RECRUITER'),('CANDIDATE')
+INSERT INTO account_details (is_active, is_delete_scheduled, is_premium_account)
+VALUES (1, 0, 1), (1, 0, 1)$$
+
+INSERT INTO roles (roleName)
+VALUES ('ADMIN'), ('MANAGER'), ('STAFF'), ('RECRUITER'), ('CANDIDATE'), ('USER')$$
+
+INSERT INTO registereduser (has_password, has_company_profile_id, has_candidate_profile, userid, email, password, confirmpassword, username, account_details_id, provider)
+VALUES
+(1, false, false, 1, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'superadmin', 1, 'local'),
+(1, false, false, 2, '<EMAIL>', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', '$2a$10$KKlKOTakQ0K5ro4s4eqFpekCsR148SluKjeS4EIoXIRLtuyHVaSti', 'marketing', 2, 'local')
+$$
+
+INSERT INTO users_roles (user_id, role_id)
+VALUES
+(1, 1),
+(2, 3)$$
+
+-- Add payment methods
+INSERT INTO payment_method (id, name, stripe_method_type, description, active) VALUES
+(1, 'Credit/Debit Card', 'card', 'Pay with credit or debit card', true),
+(2, 'Amazon Pay', 'amazon_pay', 'Pay with Amazon Pay', true),
+(3, 'PayPal', 'paypal', 'Pay with PayPal', true)$$
+
+INSERT INTO component_type (id, name) VALUES
+(1, 'skills'),
+(2, 'departments'),
+(3, 'locations'),
+(4, 'career_level'),
+(5, 'experience'),
+(6, 'manage_jobs'),
+(7, 'all_applications'),
+(8, 'qualifications'),
+(9, 'nature_of_business'),
+(10, 'company_size'),
+(11, 'employee_count_range'),
+(12, 'job_categories'),
+(13, 'job_type'),
+(14, 'salary_currency'),
+(15, 'pay_type'),
+(16, 'application_status'),
+(17, 'job_subcategories'),
+(18, 'resources'),
+(19, 'tools'),
+(20, 'resource_subcategories'),
+(21, 'seo_categories'),
+(22, 'seo_subcategories'),
+(23, 'job_subsubcategories'),
+(24, 'districts'),
+(25, 'seo_subsubcategories'),
+(26, 'permission_types'),
+(27, 'subscription_plan_permissions')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(1, 1, 'A&E'),
+(1, 2, 'Cardiology'),
+(1, 3, 'Medical'),
+(1, 4, 'General wards'),
+(1, 5, 'IT Consultant'),
+(1, 6, 'ITU/HDU'),
+(1, 7, 'Obs & Gynae'),
+(1, 8, 'Surgical'),
+(1, 9, 'Mental health'),
+(1, 10, 'Software Engineer'),
+(1, 11, 'Theatres'),
+(1, 12, 'Midwifery'),
+(1, 13, 'Orthopaedics'),
+(1, 14, 'Community'),
+(1, 15, 'Developer'),
+(1, 16, 'Endoscopy'),
+(1, 17, 'Paediatrics'),
+(1, 18, 'ODP'),
+(1, 19, 'General Practitioner'),
+(1, 20, 'HR Admin'),
+(1, 21, 'Chemotherapy'),
+(1, 22, 'Radiology'),
+(1, 23, 'Urology'),
+(1, 24, 'Nurse practitioner'),
+(1, 25, 'Testing'),
+(1, 26, 'Neonatal/PICU'),
+(1, 27, 'Palliative'),
+(1, 28, 'Dialysis'),
+(1, 29, 'ENT'),
+(1, 30, 'Others')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(2, 1, 'HR'),
+(2, 2, 'Finance'),
+(2, 3, 'Engineering'),
+(2, 4, 'Health Care'),
+(2, 5, 'Social Care')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(3, 1, 'New York'),
+(3, 2, 'London'),
+(3, 3, 'Mumbai')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(4, 1, 'Entry level'),
+(4, 2, 'Junior level'),
+(4, 3, 'Mid-Level'),
+(4, 4, 'Senior-Level'),
+(4, 5, 'Executive/Management'),
+(4, 6, 'Others')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(5, 1, 'No experience'),
+(5, 2, 'Less than 1 year'),
+(5, 3, '1-3 years'),
+(5, 4, '3-5 years'),
+(5, 5, '5-10 years'),
+(5, 6, '10-15 years'),
+(5, 7, '15+ years')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(6, 1, 'Today'),
+(6, 2, 'Last 7 days'),
+(6, 3, 'Last 30 days'),
+(6, 4, 'Last 45 days'),
+(6, 5, 'Last 60 days'),
+(6, 6, 'Last 90 days'),
+(6, 7, 'Last 6 months'),
+(6, 8, 'Last 12 months'),
+(6, 9, 'Last 24 months'),
+(6, 10, 'Last 5 years'),
+(6, 11, 'Last 7 years')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(7, 1, 'Today'),
+(7, 2, 'Last 7 days'),
+(7, 3, 'Last 30 days'),
+(7, 4, 'Last 45 days'),
+(7, 5, 'Last 60 days'),
+(7, 6, 'Last 90 days'),
+(7, 7, 'Last 6 months'),
+(7, 8, 'Last 12 months'),
+(7, 9, 'Last 24 months'),
+(7, 10, 'Last 5 years'),
+(7, 11, 'Last 7 years')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(8, 1, 'GCSE/A level'),
+(8, 2, 'Diploma'),
+(8, 3, 'Degree'),
+(8, 4, 'Post graduate'),
+(8, 5, 'Doctorate'),
+(8, 6, 'Certificate course'),
+(8, 7, 'Others')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(9, 1, 'Accounting & Finance'),
+(9, 2, 'Education'),
+(9, 3, 'Engineering'),
+(9, 4, 'Health care'),
+(9, 5, 'Information technology'),
+(9, 6, 'Social care')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(10, 1, '1-50'),
+(10, 2, '101 - 200'),
+(10, 3, '201 - 300'),
+(10, 4, '301 - 400'),
+(10, 5, '401 - 500'),
+(10, 6, '501 - 750'),
+(10, 7, '51 - 100'),
+(10, 8, '751 - 1000'),
+(10, 9, '1000+')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(11, 1, '1-10'),
+(11, 2, '11-50'),
+(11, 3, '51-100'),
+(11, 4, '101-250'),
+(11, 5, '251-500'),
+(11, 6, '501-750'),
+(11, 7, '751-1000'),
+(11, 8, '1000+')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(12, 1, 'Allied health professionals'),
+(12, 2, 'Ambulance service team'),
+(12, 3, 'Dental team'),
+(12, 4, 'Doctors'),
+(12, 5, 'Estates and facilities'),
+(12, 6, 'Health informatics'),
+(12, 7, 'Healthcare science'),
+(12, 8, 'Healthcare support worker'),
+(12, 9, 'Management'),
+(12, 10, 'Medical associate professions'),
+(12, 11, 'Midwifery'),
+(12, 12, 'Nursing'),
+(12, 13, 'Pharmacy'),
+(12, 14, 'Psychological professions'),
+(12, 15, 'Public health'),
+(12, 16, 'Wider healthcare team')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(13, 1, 'Full-time'),
+(13, 2, 'Part-time'),
+(13, 3, 'Permanent'),
+(13, 4, 'Contract'),
+(13, 5, 'Temporary'),
+(13, 6, 'Training'),
+(13, 7, 'Freelancer'),
+(13, 8, 'Volunteer')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(14, 1, 'GBP'),
+(14, 2, 'USD'),
+(14, 3, 'EUR'),
+(14, 4, 'AUD'),
+(14, 5, 'CAD'),
+(14, 6, 'INR')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(15, 1, 'Hourly'),
+(15, 2, 'Weekly'),
+(15, 3, 'Monthly'),
+(15, 4, 'Yearly')$$
+
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(16, 1, 'Applied'),
+(16, 2, 'Shortlisted'),
+(16, 3, 'Interview'),
+(16, 4, 'Hired'),
+(16, 5, 'Rejected')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 1, 'Art therapist/art psychotherapist|1'),
+(17, 2, 'Diagnostic radiographer|1'),
+(17, 3, 'Dietitian|1'),
+(17, 4, 'Dramatherapist|1'),
+(17, 5, 'Music therapist|1'),
+(17, 6, 'Occupational therapist|1'),
+(17, 7, 'Operating department practitioner|1'),
+(17, 8, 'Orthoptist|1'),
+(17, 9, 'Osteopath|1'),
+(17, 10, 'Paramedic|1'),
+(17, 11, 'Physiotherapist|1'),
+(17, 12, 'Podiatrist|1'),
+(17, 13, 'Prosthetist/orthotist|1'),
+(17, 14, 'Speech and language therapist|1'),
+(17, 15, 'Therapeutic radiographer|1')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 16, 'Ambulance care assistant and Patient Transport Service (PTS) driver|2'),
+(17, 17, 'Call handler/emergency medical dispatcher|2'),
+(17, 18, 'Emergency care assistant|2'),
+(17, 19, 'Emergency medical technician|2'),
+(17, 20, 'Experienced paramedic|2'),
+(17, 21, 'Patient transport service (PTS) call handler|2')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 22, 'Dental|3'),
+(17, 23, 'Dental nurse|3'),
+(17, 24, 'Dental technician/ dental technologist|3'),
+(17, 25, 'Dental Therapist|3'),
+(17, 26, 'Dentist|3')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 27, 'Anesthesia|4'),
+(17, 28, 'Clinical oncology|4'),
+(17, 29, 'Clinical radiology|4'),
+(17, 30, 'Community sexual and reproductive health|4'),
+(17, 31, 'Emergency medicine|4'),
+(17, 32, 'General practitioner|4'),
+(17, 33, 'Intensive care medicine|4'),
+(17, 34, 'Obstetrics and gynaecology|4'),
+(17, 35, 'Occupational medicine|4'),
+(17, 36, 'Ophthalmology|4'),
+(17, 37, 'Medicine Doctor|4'),
+(17, 38, 'Pathology Doctor|4'),
+(17, 39, 'Psychiatry Doctor|4'),
+(17, 40, 'Surgery Doctor|4'),
+(17, 41, 'Paediatrics|4')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 42, 'Domestic services|5'),
+(17, 43, 'Estates services|5'),
+(17, 44, 'Support services|5')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 45, 'Clinical informatics|6'),
+(17, 46, 'Education and training roles|6'),
+(17, 47, 'Health records and patient administration|6'),
+(17, 48, 'Information and communication technology|6'),
+(17, 49, 'Information management staff|6'),
+(17, 50, 'Knowledge and library services|6'),
+(17, 51, 'Project and programme management|6')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 52, 'Clinical bioinformatics|7'),
+(17, 53, 'Life sciences|7'),
+(17, 54, 'Physical sciences and biomedical engineering|7'),
+(17, 55, 'Physiological sciences|7')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 56, 'Dietetic assistant|8'),
+(17, 57, 'Healthcare assistant|8'),
+(17, 58, 'Healthcare support worker|8'),
+(17, 59, 'Mammography associate|8'),
+(17, 60, 'Maternity support worker|8'),
+(17, 61, 'Occupational therapy support worker|8'),
+(17, 62, 'Podiatry assistant|8'),
+(17, 63, 'Prosthetic technician|8'),
+(17, 64, 'Radiography assistants and imaging support workers|8'),
+(17, 65, 'Speech and language therapy assistant|8')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 66, 'Clinical manager|9'),
+(17, 67, 'Estates manager|9'),
+(17, 68, 'Finance manager|9'),
+(17, 69, 'General management|9'),
+(17, 70, 'Human resources (HR) manager|9'),
+(17, 71, 'Operational management|9'),
+(17, 72, 'Practice manager|9'),
+(17, 73, 'Project manager|9')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 74, 'Anaesthesia associate|10'),
+(17, 75, 'Physician associate|10'),
+(17, 76, 'Surgical care practitioner|10')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 77, 'Midwife|11')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 78, 'Adult nurse|12'),
+(17, 79, 'Childrens nurse|12'),
+(17, 80, 'District nurse|12'),
+(17, 81, 'General practice nurse|12'),
+(17, 82, 'Learning disability nurse|12'),
+(17, 83, 'Mental health nurse|12'),
+(17, 84, 'Nursing associate|12'),
+(17, 85, 'Prison nurse|12'),
+(17, 86, 'Theatre nurse|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 87, 'Pharmacist|13'),
+(17, 88, 'Pharmacy assistant|13'),
+(17, 89, 'Pharmacy technician|13')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 90, 'Adult psychotherapist|14'),
+(17, 91, 'Assistant psychologist|14'),
+(17, 92, 'CBT therapist|14'),
+(17, 93, 'Child and adolescent psychotherapist|14'),
+(17, 94, 'Childrens wellbeing practitioner|14'),
+(17, 95, 'Clinical associate in psychology|14'),
+(17, 96, 'Clinical psychologist|14'),
+(17, 97, 'Counselling psychologist|14'),
+(17, 98, 'Counsellor|14'),
+(17, 99, 'Education mental health practitioner|14'),
+(17, 100, 'Family and systemic psychotherapist|14'),
+(17, 101, 'Forensic psychologist|14'),
+(17, 102, 'Health psychologist|14'),
+(17, 103, 'High intensity therapist|14'),
+(17, 104, 'Mental health and wellbeing practitioner|14'),
+(17, 105, 'Peer support worker|14'),
+(17, 106, 'Psychological wellbeing practitioner|14'),
+(17, 107, 'Youth intensive psychological practitioner|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 108, 'Director of public health|15'),
+(17, 109, 'Environmental health professional|15'),
+(17, 110, 'Health trainer|15'),
+(17, 111, 'Health visitor|15'),
+(17, 112, 'Occupational health nurse|15'),
+(17, 113, 'Public health academic|15'),
+(17, 114, 'Public health consultants and specialists|15'),
+(17, 115, 'Public health knowledge and intelligence professional|15'),
+(17, 116, 'Public health manager|15'),
+(17, 117, 'Public health nurse|15'),
+(17, 118, 'Public health practitioner|15'),
+(17, 119, 'School nurse|15')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 120, 'Wider healthcare team|16'),
+(17, 121, 'Clinical support staff|16'),
+(17, 122, 'Corporate services|16')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 1, 'CV Writing Tips|11'),
+(20, 2, 'Interview Preparation|11'),
+(20, 3, 'Career Development|11'),
+(20, 4, 'Job Market Trends|11'),
+(20, 5, 'Healthcare Industry News|11')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 6, 'CV Templates|12'),
+(20, 7, 'CV Help & Tips|12'),
+(20, 8, 'Personal Statement|12'),
+(20, 9, 'Interview Questions|12'),
+(20, 10, 'Interview Advice|12'),
+(20, 11, 'Career Development|12'),
+(20, 12, 'Job Applications|12'),
+(20, 13, 'Career Change|12'),
+(20, 14, 'Job Descriptions|12'),
+(20, 15, 'Starting a New Job|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 16, 'Nursing Roles|13'),
+(20, 17, 'Doctor Roles|13'),
+(20, 18, 'Allied Health Roles|13'),
+(20, 19, 'Administrative Roles|13'),
+(20, 20, 'Support Staff Roles|13')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 21, 'Hiring Tips|14'),
+(20, 22, 'Job Posting Guidelines|14'),
+(20, 23, 'Candidate Screening|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(21, 1, 'Blog'),
+(21, 2, 'Career Guide'),
+(21, 3, 'Explore Roles'),
+(21, 4, 'Employer Resource')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 1, 'Tips|1'),
+(22, 2, 'News|1'),
+(22, 3, 'Tutorials|1'),
+(22, 4, 'Case Studies|1')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 5, 'Resume|2'),
+(22, 6, 'Interview|2'),
+(22, 7, 'Job Search|2'),
+(22, 8, 'Career Development|2')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 9, 'Healthcare|3'),
+(22, 10, 'Technology|3'),
+(22, 11, 'Finance|3'),
+(22, 12, 'Education|3')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 13, 'Hiring|4'),
+(22, 14, 'Retention|4'),
+(22, 15, 'Workplace|4'),
+(22, 16, 'Compliance|4')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(20, 24, 'Employer Branding|14'),
+(20, 25, 'Retention Strategies|14')$$
+
+-- add job sub-subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 1, 'Acute internal medicine|37'),
+(23, 2, 'Allergy|37'),
+(23, 3, 'Audiovestibular medicine|37'),
+(23, 4, 'Cardiologist|37'),
+(23, 5, 'Clinical genetics|37'),
+(23, 6, 'Clinical neurophysiology|37'),
+(23, 7, 'Clinical pharmacology and therapeutics|37'),
+(23, 8, 'Dermatology|37'),
+(23, 9, 'Endocrinology and diabetes|37'),
+(23, 10, 'Gastroenterology|37'),
+(23, 11, 'General internal medicine|37'),
+(23, 12, 'Genitourinary medicine|37'),
+(23, 13, 'Geriatric medicine|37'),
+(23, 14, 'Immunology|37'),
+(23, 15, 'Infectious diseases|37'),
+(23, 16, 'Medical oncology|37'),
+(23, 17, 'Medical ophthalmology|37'),
+(23, 18, 'Metabolic Medicine|37'),
+(23, 19, 'Neurologist|37'),
+(23, 20, 'Nuclear medicine|37'),
+(23, 21, 'Palliative medicine|37'),
+(23, 22, 'Pharmaceutical medicine|37'),
+(23, 23, 'Rehabilitation medicine|37'),
+(23, 24, 'Renal medicine|37'),
+(23, 25, 'Respiratory medicine|37'),
+(23, 26, 'Rheumatology|37'),
+(23, 27, 'Sport and exercise medicine|37'),
+(23, 28, 'Stroke medicine|37'),
+(23, 29, 'Tropical medicine|37')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 30, 'Chemical pathology|38'),
+(23, 31, 'Haematology (doctor)|38'),
+(23, 32, 'Histopathology (doctor)|38'),
+(23, 33, 'Medical microbiology and virology (doctor)|38')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 34, 'Child and adolescent psychiatry|39'),
+(23, 35, 'Forensic psychiatry|39'),
+(23, 36, 'General psychiatry|39'),
+(23, 37, 'Liaison psychiatry|39'),
+(23, 38, 'Medical psychotherapy|39'),
+(23, 39, 'Old age psychiatry|39'),
+(23, 40, 'Psychiatry of intellectual disability (PID)|39')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 41, 'Cardiothoracic surgeon|40'),
+(23, 42, 'General surgery|40'),
+(23, 43, 'Neurosurgeon|40'),
+(23, 44, 'Oral and maxillofacial surgery|40'),
+(23, 45, 'Otorhinolaryngology (ear, nose and throat (ENT) surgery)|40'),
+(23, 46, 'Paediatric surgery|40'),
+(23, 47, 'Plastic surgery|40'),
+(23, 48, 'Trauma and orthopaedic surgery|40'),
+(23, 49, 'Urology|40'),
+(23, 50, 'Vascular surgery|40')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 51, 'Paediatric cardiology|41'),
+(23, 52, 'Paediatrician|41')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 53, 'Communications and corporate affairs|69'),
+(23, 54, 'Performance and quality management|69'),
+(23, 55, 'Purchasing and contract management|69'),
+(23, 56, 'Strategic management|69')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 57, 'Administrative management|71'),
+(23, 58, 'Decontamination services management|71'),
+(23, 59, 'Facilities management|71'),
+(23, 60, 'Hotel services management|71'),
+(23, 61, 'Integrated urgent care/NHS 111 team leader|71')$$
+
+-- Add districts
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(24, 1, 'Chennai'),
+(24, 2, 'Bangalore'),
+(24, 3, 'Hyderabad'),
+(24, 4, 'Mumbai')$$
+
+-- Add SEO subsubcategories with proper parent-child relationships
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 1, 'Resume Writing Tips|5'),
+(25, 2, 'Cover Letter Examples|5'),
+(25, 3, 'CV Templates|5'),
+(25, 4, 'Personal Statement Guide|5'),
+(25, 5, 'Job Application Tips|5'),
+(25, 6, 'Interview Preparation|6'),
+(25, 7, 'Common Interview Questions|6'),
+(25, 8, 'Interview Techniques|6'),
+(25, 9, 'Interview Attire|6'),
+(25, 10, 'Interview Follow-up|6')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 11, 'Job Search Strategies|7'),
+(25, 12, 'Online Job Boards|7'),
+(25, 13, 'Networking Tips|7'),
+(25, 14, 'Social Media Job Search|7'),
+(25, 15, 'Job Fairs|7'),
+(25, 16, 'Career Planning|8'),
+(25, 17, 'Career Change Advice|8'),
+(25, 18, 'Professional Development|8'),
+(25, 19, 'Continuing Education|8'),
+(25, 20, 'Promotion Strategies|8')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 21, 'Healthcare Careers|9'),
+(25, 22, 'Medical Professions|9'),
+(25, 23, 'Nursing Careers|9'),
+(25, 24, 'Allied Health Professions|9'),
+(25, 25, 'Healthcare Administration|9'),
+(25, 26, 'Software Development|10'),
+(25, 27, 'Data Science|10'),
+(25, 28, 'Cybersecurity|10'),
+(25, 29, 'IT Support|10'),
+(25, 30, 'Web Development|10')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 31, 'Banking Careers|11'),
+(25, 32, 'Investment Banking|11'),
+(25, 33, 'Financial Analysis|11'),
+(25, 34, 'Accounting Careers|11'),
+(25, 35, 'Insurance Industry|11'),
+(25, 36, 'Teaching Careers|12'),
+(25, 37, 'Education Administration|12'),
+(25, 38, 'Higher Education|12'),
+(25, 39, 'Special Education|12'),
+(25, 40, 'Early Childhood Education|12')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 41, 'Recruitment Best Practices|13'),
+(25, 42, 'Talent Acquisition|13'),
+(25, 43, 'Interviewing Techniques|13'),
+(25, 44, 'Candidate Screening|13'),
+(25, 45, 'Job Description Writing|13'),
+(25, 46, 'Employee Retention|14'),
+(25, 47, 'Staff Development|14'),
+(25, 48, 'Employee Benefits|14'),
+(25, 49, 'Recognition Programs|14'),
+(25, 50, 'Reducing Turnover|14')$$
+
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(25, 51, 'Remote Work Policies|15'),
+(25, 52, 'Office Environment|15'),
+(25, 53, 'Team Building|15'),
+(25, 54, 'Workplace Culture|15'),
+(25, 55, 'Work-Life Balance|15'),
+(25, 56, 'Employment Law|16'),
+(25, 57, 'HR Compliance|16'),
+(25, 58, 'Workplace Safety|16'),
+(25, 59, 'Data Protection|16'),
+(25, 60, 'Equal Opportunity|16')$$
+
+-- Add job categories data to SEO categories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(21, 5, 'Allied health professionals'),
+(21, 6, 'Ambulance service team'),
+(21, 7, 'Dental team'),
+(21, 8, 'Doctors'),
+(21, 9, 'Estates and facilities'),
+(21, 10, 'Health informatics'),
+(21, 11, 'Healthcare science'),
+(21, 12, 'Healthcare support worker'),
+(21, 13, 'Management'),
+(21, 14, 'Medical associate professions'),
+(21, 15, 'Midwifery'),
+(21, 16, 'Nursing'),
+(21, 17, 'Pharmacy'),
+(21, 18, 'Psychological professions'),
+(21, 19, 'Public health'),
+(21, 20, 'Wider healthcare team')$$
+
+-- Add job subcategories data to SEO subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(22, 17, 'Art therapist/art psychotherapist|5'),
+(22, 18, 'Diagnostic radiographer|5'),
+(22, 19, 'Dietitian|5'),
+(22, 20, 'Dramatherapist|5'),
+(22, 21, 'Music therapist|5'),
+(22, 22, 'Occupational therapist|5'),
+(22, 23, 'Operating department practitioner|5'),
+(22, 24, 'Orthoptist|5'),
+(22, 25, 'Osteopath|5'),
+(22, 26, 'Paramedic|5'),
+(22, 27, 'Physiotherapist|5'),
+(22, 28, 'Podiatrist|5'),
+(22, 29, 'Prosthetist/orthotist|5'),
+(22, 30, 'Speech and language therapist|5'),
+(22, 31, 'Therapeutic radiographer|5'),
+(22, 32, 'Ambulance care assistant and Patient Transport Service (PTS) driver|6'),
+(22, 33, 'Call handler/emergency medical dispatcher|6'),
+(22, 34, 'Emergency care assistant|6'),
+(22, 35, 'Emergency medical technician|6'),
+(22, 36, 'Experienced paramedic|6'),
+(22, 37, 'Patient transport service (PTS) call handler|6'),
+(22, 38, 'Dental|7'),
+(22, 39, 'Dental nurse|7'),
+(22, 40, 'Dental technician/ dental technologist|7'),
+(22, 41, 'Dental Therapist|7'),
+(22, 42, 'Dentist|7'),
+(22, 43, 'Anesthesia|8'),
+(22, 44, 'Clinical oncology|8'),
+(22, 45, 'Clinical radiology|8'),
+(22, 46, 'Community sexual and reproductive health|8'),
+(22, 47, 'Emergency medicine|8'),
+(22, 48, 'General practitioner|8'),
+(22, 49, 'Intensive care medicine|8'),
+(22, 50, 'Obstetrics and gynaecology|8'),
+(22, 51, 'Occupational medicine|8'),
+(22, 52, 'Ophthalmology|8'),
+(22, 53, 'Medicine Doctor|8'),
+(22, 54, 'Pathology Doctor|8'),
+(22, 55, 'Psychiatry Doctor|8'),
+(22, 56, 'Surgery Doctor|8'),
+(22, 57, 'Paediatrics|8')$$
+
+-- Add SEO subcategories for healthcare-related categories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+-- Dental team subcategories
+(22, 58, 'Dental News|7'),
+(22, 59, 'Dental Careers|7'),
+(22, 60, 'Dental Education|7'),
+(22, 61, 'Dental Technology|7'),
+
+-- Doctors subcategories
+(22, 62, 'Medical News|8'),
+(22, 63, 'Medical Research|8'),
+(22, 64, 'Clinical Practice|8'),
+(22, 65, 'Medical Education|8'),
+
+-- Nursing subcategories
+(22, 66, 'Nursing News|16'),
+(22, 67, 'Nursing Careers|16'),
+(22, 68, 'Nursing Education|16'),
+(22, 69, 'Clinical Skills|16')$$
+
+-- Add SEO subsubcategories with correct parent IDs referencing SEO subcategories
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+-- Medical topics under Medical News (subcategory_id 62)
+(25, 1, 'Acute internal medicine|62'),
+(25, 2, 'Allergy|62'),
+(25, 3, 'Audiovestibular medicine|62'),
+(25, 4, 'Cardiologist|62'),
+(25, 5, 'Clinical genetics|62'),
+(25, 6, 'Clinical neurophysiology|62'),
+(25, 7, 'Clinical pharmacology and therapeutics|62'),
+(25, 8, 'Dermatology|62'),
+(25, 9, 'Endocrinology and diabetes|62'),
+(25, 10, 'Gastroenterology|62'),
+(25, 11, 'General internal medicine|62'),
+(25, 12, 'Genitourinary medicine|62'),
+(25, 13, 'Geriatric medicine|62'),
+(25, 14, 'Immunology|62'),
+(25, 15, 'Infectious diseases|62'),
+
+-- Medical specialties under Clinical Practice (subcategory_id 64)
+(25, 16, 'Medical oncology|64'),
+(25, 17, 'Medical ophthalmology|64'),
+(25, 18, 'Metabolic Medicine|64'),
+(25, 19, 'Neurologist|64'),
+(25, 20, 'Nuclear medicine|64'),
+(25, 21, 'Palliative medicine|64'),
+(25, 22, 'Pharmaceutical medicine|64'),
+(25, 23, 'Rehabilitation medicine|64'),
+(25, 24, 'Renal medicine|64'),
+(25, 25, 'Respiratory medicine|64'),
+
+-- Surgical specialties under Dental News (subcategory_id 58)
+(25, 26, 'Dental Surgery|58'),
+(25, 27, 'Orthodontics|58'),
+(25, 28, 'Periodontics|58'),
+(25, 29, 'Endodontics|58'),
+(25, 30, 'Prosthodontics|58'),
+(25, 31, 'Pediatric Dentistry|58'),
+(25, 32, 'Oral Medicine|58'),
+(25, 33, 'Dental Public Health|58'),
+
+-- Psychiatric specialties under Medical Research (subcategory_id 63)
+(25, 34, 'Child and adolescent psychiatry|63'),
+(25, 35, 'Forensic psychiatry|63'),
+(25, 36, 'General psychiatry|63'),
+(25, 37, 'Liaison psychiatry|63'),
+(25, 38, 'Medical psychotherapy|63'),
+(25, 39, 'Old age psychiatry|63'),
+(25, 40, 'Psychiatry of intellectual disability|63'),
+
+-- Surgical specialties under Medical Education (subcategory_id 65)
+(25, 41, 'Cardiothoracic surgery|65'),
+(25, 42, 'General surgery|65'),
+(25, 43, 'Neurosurgery|65'),
+(25, 44, 'Oral and maxillofacial surgery|65'),
+(25, 45, 'Otorhinolaryngology|65'),
+(25, 46, 'Paediatric surgery|65'),
+(25, 47, 'Plastic surgery|65'),
+(25, 48, 'Trauma and orthopaedic surgery|65'),
+(25, 49, 'Urology|65'),
+(25, 50, 'Vascular surgery|65')$$
+
+-- Add master data for permission types
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(26, 0, 'VIEW_APPLICANTS'),
+(26, 1, 'CONTACT_APPLICANTS'),
+(26, 2, 'POST_JOBS'),
+(26, 3, 'FEATURED_JOBS'),
+(26, 4, 'ANALYTICS'),
+(26, 5, 'BULK_ACTIONS')$$
+
+-- Add master data for subscription plan permissions
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(27, 0, '000000'), -- Free Plan - No permissions
+(27, 1, '000000'), -- Basic Plan - Not used, no permissions
+(27, 2, '101110'), -- Standard Plan ("starter") - VIEW_APPLICANTS, POST_JOBS, FEATURED_JOBS, BULK_ACTIONS
+(27, 3, '111110'), -- Premium Plan ("advance") - VIEW_APPLICANTS, CONTACT_APPLICANTS, POST_JOBS, FEATURED_JOBS, ANALYTICS
+(27, 4, '111111'), -- Enterprise Plan - All permissions
+(27, 5, '101110')  -- Trial Plan - Same as Standard Plan
 $$
+
+-- Add subscription plan data with permissions (without plan_object as it will store Stripe price IDs)
+-- Plan IDs match the constants in ConstantsUtil.java:
+-- SUBSCRIPTION_STANDARD_PLAN = 2
+-- SUBSCRIPTION_PREMIUM_PLAN = 3
+-- SUBSCRIPTION_ENTERPRISE_PLAN = 4
+-- SUBSCRIPTION_TRIAL_PLAN = 5
+INSERT INTO subscription_plan (plan_id, plan_name, permissions) VALUES
+(5, 'Trial', '101110'),
+(2, 'Standard Monthly', '101110'),
+(20, 'Standard Yearly', '101110'),
+(3, 'Premium Monthly', '111110'),
+(30, 'Premium Yearly', '111110'),
+(4, 'Enterprise Monthly', '111111'),
+(40, 'Enterprise Yearly', '111111')
+$$
+
+-- Add subcategories for Estates and facilities (category_id 5)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 123, 'Catering manager|5'),
+(17, 124, 'Chef/cook|5'),
+(17, 125, 'Domestic services staff|5'),
+(17, 126, 'Housekeeper|5'),
+(17, 127, 'Linen services staff|5'),
+(17, 128, 'Bricklayer|5'),
+(17, 129, 'Caretaker|5'),
+(17, 130, 'Carpenter/joiner|5'),
+(17, 131, 'Electrician|5'),
+(17, 132, 'Engineer|5'),
+(17, 133, 'Estates technician|5'),
+(17, 134, 'Gardeners and grounds staff|5'),
+(17, 135, 'Painter and decorator|5'),
+(17, 136, 'Plumber|5'),
+(17, 137, 'Surveyor|5'),
+(17, 138, 'Tiler|5'),
+(17, 139, 'Window cleaner|5'),
+(17, 140, 'Driver|5'),
+(17, 141, 'Fire safety officer|5'),
+(17, 142, 'Health and safety officer|5'),
+(17, 143, 'Porter|5'),
+(17, 144, 'Security staff|5'),
+(17, 145, 'Stores and distribution staff|5')$$
+
+-- Add subcategories for Health informatics (category_id 6)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 146, 'Clinical bioinformatics|6')$$
+
+-- Add subcategories for Healthcare science (category_id 7)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 147, 'Clinical bioinformatics (genomics)|7'),
+(17, 148, 'Clinical bioinformatics (health informatics)|7'),
+(17, 149, 'Clinical bioinformatics (physical sciences)|7')$$
+
+-- Add subcategories for Wider healthcare team (category_id 16)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(17, 150, 'Clerk|16'),
+(17, 151, 'Health records staff|16'),
+(17, 152, 'Medical secretary/personal assistant|16'),
+(17, 153, 'Receptionist|16'),
+(17, 154, 'Secretary/typist|16'),
+(17, 155, 'Telephonist/switchboard operator|16'),
+(17, 156, 'Assistant practitioner|16'),
+(17, 157, 'Cardiographer|16'),
+(17, 158, 'Creative therapy support roles|16'),
+(17, 159, 'Dental support worker|16'),
+(17, 160, 'Donor carer|16'),
+(17, 161, 'Employment specialist|16'),
+(17, 162, 'Health play staff|16'),
+(17, 163, 'Healthcare science assistants and associates|16'),
+(17, 164, 'Integrated urgent care/NHS 111 roles|16'),
+(17, 165, 'Mammographer|16'),
+(17, 166, 'Medical support worker|16'),
+(17, 167, 'Newborn hearing screener|16'),
+(17, 168, 'Nutritionist|16'),
+(17, 169, 'Optometrist|16'),
+(17, 170, 'Orthopaedic practitioner|16'),
+(17, 171, 'Phlebotomist|16'),
+(17, 172, 'Social prescribing link worker|16'),
+(17, 173, 'Social worker|16'),
+(17, 174, 'Support, time and recovery worker|16'),
+(17, 175, 'Theatre support worker|16'),
+(17, 176, 'Arts manager/arts co-ordinator|16'),
+(17, 177, 'Chaplain|16'),
+(17, 178, 'Communications/public relations staff|16'),
+(17, 179, 'Finance staff|16'),
+(17, 180, 'Human resources staff|16'),
+(17, 181, 'Nursery nurse and nursery assistant|16')$$
+
+-- Add sub-subcategories for Domestic services (subcategory_id 42)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 62, 'Catering manager|42'),
+(23, 63, 'Chef/cook|42'),
+(23, 64, 'Domestic services staff|42'),
+(23, 65, 'Housekeeper|42'),
+(23, 66, 'Linen services staff|42')$$
+
+-- Add sub-subcategories for Estates services (subcategory_id 43)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 67, 'Bricklayer|43'),
+(23, 68, 'Caretaker|43'),
+(23, 69, 'Carpenter/joiner|43'),
+(23, 70, 'Electrician|43'),
+(23, 71, 'Engineer|43'),
+(23, 72, 'Estates technician|43'),
+(23, 73, 'Gardeners and grounds staff|43'),
+(23, 74, 'Painter and decorator|43'),
+(23, 75, 'Plumber|43'),
+(23, 76, 'Surveyor|43'),
+(23, 77, 'Tiler|43'),
+(23, 78, 'Window cleaner|43')$$
+
+-- Add sub-subcategories for Support services (subcategory_id 44)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 79, 'Driver|44'),
+(23, 80, 'Fire safety officer|44'),
+(23, 81, 'Health and safety officer|44'),
+(23, 82, 'Porter|44'),
+(23, 83, 'Security staff|44'),
+(23, 84, 'Stores and distribution staff|44')$$
+
+-- Add sub-subcategories for Clinical bioinformatics (subcategory_id 52)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 85, 'Clinical bioinformatics (genomics)|52'),
+(23, 86, 'Clinical bioinformatics (health informatics)|52'),
+(23, 87, 'Clinical bioinformatics (physical sciences)|52')$$
+
+-- Add sub-subcategories for Life sciences (subcategory_id 53)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 88, 'Analytical toxicology|53'),
+(23, 89, 'Anatomical pathology|53'),
+(23, 90, 'Biomedical science|53'),
+(23, 91, 'Cancer genomics|53'),
+(23, 92, 'Clinical biochemistry|53'),
+(23, 93, 'Clinical immunology|53'),
+(23, 94, 'Cytopathology|53'),
+(23, 95, 'Genomic counselling|53'),
+(23, 96, 'Genomics|53'),
+(23, 97, 'Haematology (healthcare scientist)|53'),
+(23, 98, 'Infection sciences|53'),
+(23, 99, 'Microbiology (healthcare scientist)|53'),
+(23, 100, 'Reproductive science and andrology|53'),
+(23, 101, 'Virology (healthcare scientist)|53')$$
+
+-- Add sub-subcategories for Physical sciences and biomedical engineering (subcategory_id 54)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 102, 'Clinical measurement|54'),
+(23, 103, 'Clinical or medical technology in medical physics|54'),
+(23, 104, 'Clinical pharmaceutical science|54'),
+(23, 105, 'Clinical photography|54'),
+(23, 106, 'Decontamination science (sterile services and flexible endoscopy)|54'),
+(23, 107, 'Imaging (ionising)|54'),
+(23, 108, 'Imaging (non-ionising)|54'),
+(23, 109, 'Medical device risk management and governance|54'),
+(23, 110, 'Medical engineering|54'),
+(23, 111, 'Nuclear medicine (healthcare scientist)|54'),
+(23, 112, 'Radiation physics and radiation safety physics|54'),
+(23, 113, 'Radiotherapy physics|54'),
+(23, 114, 'Reconstructive science|54'),
+(23, 115, 'Rehabilitation engineering|54'),
+(23, 116, 'Renal technology|54')$$
+
+-- Add sub-subcategories for Physiological sciences (subcategory_id 55)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 117, 'Audiology|55'),
+(23, 118, 'Cardiac sciences|55'),
+(23, 119, 'Clinical exercise physiologist|55'),
+(23, 120, 'Clinical perfusion science|55'),
+(23, 121, 'Critical care science|55'),
+(23, 122, 'Gastrointestinal physiology|55'),
+(23, 123, 'Hearing aid dispenser|55'),
+(23, 124, 'Neurophysiology|55'),
+(23, 125, 'Ophthalmic and vision science|55'),
+(23, 126, 'Respiratory physiology and sleep sciences|55'),
+(23, 127, 'Urodynamic science|55'),
+(23, 128, 'Vascular science|55')$$
+
+-- Add sub-subcategories for Wider healthcare team (subcategory_id 120)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 129, 'Clerk|120'),
+(23, 130, 'Health records staff|120'),
+(23, 131, 'Medical secretary/personal assistant|120'),
+(23, 132, 'Receptionist|120'),
+(23, 133, 'Secretary/typist|120'),
+(23, 134, 'Telephonist/switchboard operator|120')$$
+
+-- Add sub-subcategories for Clinical support staff (subcategory_id 121)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 135, 'Assistant practitioner|121'),
+(23, 136, 'Cardiographer|121'),
+(23, 137, 'Creative therapy support roles|121'),
+(23, 138, 'Dental support worker|121'),
+(23, 139, 'Donor carer|121'),
+(23, 140, 'Employment specialist|121'),
+(23, 141, 'Health play staff|121'),
+(23, 142, 'Healthcare science assistants and associates|121'),
+(23, 143, 'Integrated urgent care/NHS 111 roles|121'),
+(23, 144, 'Mammographer|121'),
+(23, 145, 'Medical support worker|121'),
+(23, 146, 'Newborn hearing screener|121'),
+(23, 147, 'Nutritionist|121'),
+(23, 148, 'Optometrist|121'),
+(23, 149, 'Orthopaedic practitioner|121'),
+(23, 150, 'Phlebotomist|121'),
+(23, 151, 'Social prescribing link worker|121'),
+(23, 152, 'Social worker|121'),
+(23, 153, 'Support, time and recovery worker|121'),
+(23, 154, 'Theatre support worker|121')$$
+
+-- Add sub-subcategories for Corporate services (subcategory_id 122)
+INSERT INTO master_data (component_type_id, master_data_id, value) VALUES
+(23, 155, 'Arts manager/arts co-ordinator|122'),
+(23, 156, 'Chaplain|122'),
+(23, 157, 'Communications/public relations staff|122'),
+(23, 158, 'Finance staff|122'),
+(23, 159, 'Human resources staff|122'),
+(23, 160, 'Nursery nurse and nursery assistant|122')$$
+
Index: target/classes/messages.properties
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>msg.course_created_success=course created successfully\r\nmsg.course_get_success=course get successfully\r\nmsg.course_update_success=course updated successfully\r\nmsg.course_update_failed=course updated failed\r\nmsg.course_deleted_success=course deleted successfully\r\nmsg.course_deleted_warning=action failed . Only course which has draft status can be deleted \r\nmsg.course_publish_success=course published successfully\r\nmsg.course_draft_success=course changed to draft successfully\r\nmsg.topic_move_success=topic moved successfully\r\nmsg.quiz_created_success=quiz created successfully\r\nmsg.quiz_get_success=quiz get successfully\r\nmsg.quiz_update_success=quiz updated successfully\r\nmsg.quiz_update_failed=quiz updated failed\r\nmsg.quiz_deleted_success=quiz deleted successfully\r\nmsg.quiz_publish_success=quiz published successfully\r\nmsg.quiz_draft_success=quiz changed to draft successfully\r\nmsg.question_created_success=question created successfully\r\nmsg.question_get_success=question get successfully\r\nmsg.question_update_success=question updated successfully\r\nmsg.question_deleted_success=question deleted successfully\r\nmsg.question_update_failed=question updated failed\r\nmsg.addtocart_get_success=addtocart get successfull\r\nmsg.addtocart_added_success=addtocart Added successFully\r\nmsg.addtocart_deleted_success=addtocart Removed successFully\r\nmsg.answer_saved_success=answer saved successfully\r\nmsg.answer_updated_success=answer updated successfully\r\nmsg.login_success=login successful\r\nmsg.user_inactive=user is inactive for long time or blocked by administrator please contact us \r\nmsg.user_created_success=user created  successful\r\nmsg.user_social_created_failed=user registration failed \r\nmsg.userdetails_changed_success=User Details changed successfully\r\nmsg.userdetails_get_success=User Details get successfully\r\nmsg.resource_added_success=Resource added successfully\r\nmsg.resource_get_success=Resource get successfully\r\nmsg.resource_deleted_success=Resource deleted successfully\r\nmsg.thumbnail_added_success=Thumbnail added successfully\r\n#marketing\r\nmsg.email_sent_success=email sent successfully\r\nmsg.SMS_sent_success=SMS send successfully\r\nmsg.emailtemplate_get_success=email template get successfully\r\nmsg.emailtemplate_added_success=email template added successfully\r\nmsg.emailtemplate_updated_success=email template updated successfully\r\nmsg.emailtemplate_deleted_success=email template deleted successfully\r\nmsg.SMStemplate_get_success=SMS template get successfully\r\nmsg.SMStemplate_added_success=SMS template added successfully\r\nmsg.SMStemplate_updated_success=SMS template updated successfully\r\nmsg.SMStemplate_deleted_success=SMS template deleted successfully\r\nmsg.blog_added_success=Blog added successfully\r\nmsg.blog_updated_success=Blog updated successfully\r\nmsg.blog_deleted_success=Blog deleted successfully\r\nmsg.blog_get_success=Blog get successfully\r\nmsg.blogdetails_get_success=Blog details get successfully\r\n#event\r\nmsg.event_added_success=Event added successfully\r\nmsg.event_updated_success=Event updated successfully\r\nmsg.event_deleted_success=Event deleted successfully\r\nmsg.event_get_success=Event get successfully\r\nmsg.eventdetails_get_success=Event details get successfully\r\n#seopages\r\nmsg.seopages_added_success=SeoPages added successfully\r\nmsg.seopages_updated_success=SeoPages updated successfully\r\nmsg.seopages_deleted_success=SeoPages deleted successfully\r\nmsg.seopages_get_success=SeoPages get successfully\r\nmsg.seopagesdetails_get_success=SeoPages details get successfully\r\n\r\nmsg.otp_sent_success=OTP sent successfully\r\nmsg.otp_verify_success=OTP verified successfully\r\nmsg.otp_resend_success=OTP Re-sent successfully\r\nmsg.something_went_wrong=Something went wrong\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>ISO-8859-1
===================================================================
diff --git a/target/classes/messages.properties b/target/classes/messages.properties
--- a/target/classes/messages.properties	(revision f3952ddefb8896ee4cd4f8d6bc29dde769d83fe3)
+++ b/target/classes/messages.properties	(date 1747380341425)
@@ -3,7 +3,7 @@
 msg.course_update_success=course updated successfully
 msg.course_update_failed=course updated failed
 msg.course_deleted_success=course deleted successfully
-msg.course_deleted_warning=action failed . Only course which has draft status can be deleted 
+msg.course_deleted_warning=action failed . Only course which has draft status can be deleted
 msg.course_publish_success=course published successfully
 msg.course_draft_success=course changed to draft successfully
 msg.topic_move_success=topic moved successfully
@@ -25,19 +25,31 @@
 msg.answer_saved_success=answer saved successfully
 msg.answer_updated_success=answer updated successfully
 msg.login_success=login successful
-msg.user_inactive=user is inactive for long time or blocked by administrator please contact us 
+
+msg.role_mismatch=role mismatch
+msg.user_inactive=user is inactive for long time or blocked by administrator please contact us
 msg.user_created_success=user created  successful
-msg.user_social_created_failed=user registration failed 
-msg.userdetails_changed_success=User Details changed successfully
+msg.user_social_created_failed=user registration failed
+
 msg.userdetails_get_success=User Details get successfully
+
+msg.login_failed=User Email or password is wrong
+msg.user_not_found=User not found
+
+
+
+
 msg.resource_added_success=Resource added successfully
 msg.resource_get_success=Resource get successfully
 msg.resource_deleted_success=Resource deleted successfully
 msg.thumbnail_added_success=Thumbnail added successfully
 #marketing
 msg.email_sent_success=email sent successfully
+msg.bulk_email_sent_success=Bulk email sent successfully to all selected applicants
 msg.SMS_sent_success=SMS send successfully
 msg.emailtemplate_get_success=email template get successfully
+msg.keywords_required=At least one keyword is required
+msg.invalid_keywords_format=Keywords must contain only letters, numbers, and spaces
 msg.emailtemplate_added_success=email template added successfully
 msg.emailtemplate_updated_success=email template updated successfully
 msg.emailtemplate_deleted_success=email template deleted successfully
@@ -49,7 +61,7 @@
 msg.blog_updated_success=Blog updated successfully
 msg.blog_deleted_success=Blog deleted successfully
 msg.blog_get_success=Blog get successfully
-msg.blogdetails_get_success=Blog details get successfully
+msg.blog_details_get_success=Blog details get successfully
 #event
 msg.event_added_success=Event added successfully
 msg.event_updated_success=Event updated successfully
@@ -65,6 +77,242 @@
 
 msg.otp_sent_success=OTP sent successfully
 msg.otp_verify_success=OTP verified successfully
+msg.otp_verify_failed=OTP Verification Failed
 msg.otp_resend_success=OTP Re-sent successfully
 msg.something_went_wrong=Something went wrong
 
+# Company Profile Messages
+msg.company_profile_created=Company profile added successfully
+msg.company_profile_updated=Company profile updated successfully
+msg.company_profile_fetched=Company profile details fetched successfully
+msg.company_profiles_active_fetched=Non-archived and active companies fetched successfully
+msg.company_profiles_archived_fetched=Archived companies fetched successfully
+msg.company_profile_archived=Company archived successfully
+msg.company_profile_restored=Company restored successfully
+msg.company_profile_deleted=Company deleted successfully
+msg.company_profile_activated=Company activated successfully
+msg.company_profile_deactivated=Company deactivated successfully
+msg.no_company_profile=No company profile found
+
+# Request
+msg.request_failed=Request failed
+
+#dashboard
+msg.dashboard_details_get_success=DashBoard Details get successfully
+
+#ticket
+msg.ticket_raised_success=Ticket Raised successfully
+msg.feedback_submitted_success=Feedback submitted successfully
+msg.feedback_get_success=Feedback get successfully
+
+# notification
+msg.notification_get_all_success=get all notification successfully
+msg.notification_added_success=Notification added successfully
+
+# notes
+msg.dairynotes_details_added_success=DairyNotes Details added successfully
+
+# profileDetails
+msg.profile_details_get_success=Profile Details get successfully
+msg.profile_details_added_success=Profile Details added successfully
+msg.profile_details_updated_success=Profile Details updated successfully
+
+# rating review
+msg.rating_review_get_success=Rating and Review get successfully
+msg.rating_review_delete_success=Rating and Review delete successfully
+msg.rating_review_save_success=Rating and Review save successfully
+
+# password
+msg.password_mismatch=Password does not match
+msg.password_change_success=password changed successfully
+msg.password_add_success=password added successfully
+msg.userdetails_changed_success=User Details changed successfully
+
+# job posts
+msg.job_created=Job created successfully
+msg.jobs_fetched=Jobs fetched successfully
+msg.job_fetched=Job fetched successfully
+msg.job_updated=Job updated successfully
+msg.job_deleted=Job deleted successfully
+msg.job_status_updated=Job status updated successfully
+msg.job_title_required=Job title is required
+msg.job_title_id_required=jobTitleId is required
+msg.valid_contact_email=Valid contact email is required
+msg.min_salary_invalid=Min salary cannot be greater than max salary
+msg.company_profile_required=Company profile ID is required
+msg.user_required=User ID is required
+msg.job_not_found=Job not found
+msg.company_not_found=Company not found
+msg.invalid_job_subcategory=Invalid job subcategory
+msg.invalid_job_subcategory_name=Invalid job subcategory name
+msg.subcategory_not_match_category=The selected subcategory does not belong to the selected category
+msg.category_id_required=categoryId is required
+msg.subcategory_id_required=subcategoryId is required
+msg.city_id_required=cityId is required
+
+# candidate profile
+msg.candidate_profile_fetched=Candidate profile fetched successfully
+msg.candidates_fetched=Candidates fetched successfully
+msg.candidate_profile_created=Candidate profile created successfully
+msg.candidate_profile_updated=Candidate profile updated successfully
+msg.candidate_activated=Candidate activated successfully
+msg.candidate_deactivated=Candidate deactivated successfully
+
+msg.registered_user_not_found=Registered User Not found
+msg.registered_user_no_linked_company=Company Not Linked To Registered User
+
+# master data
+msg.master_data_fetched=Master data fetched successfully
+msg.master_data_not_found=No master data found for componentTypeIds: {0}
+msg.mapping_error=Error mapping data
+msg.invalid_component_type_ids=Component type IDs must not be null, empty, or contain invalid values
+msg.subsubcategories_fetched=Job sub-subcategories fetched successfully.
+msg.districts_fetched=Districts fetched successfully.
+msg.job_skills_fetched=Job skills fetched successfully.
+
+# resume
+msg.resume.created=Resume created successfully
+msg.resumes.fetched=Resumes fetched successfully
+msg.resume.updated=Resume updated successfully
+msg.resume.deleted=Resume deleted successfully
+msg.resume.title_exists=Resume with this title already exists
+msg.resume.not_found=Resume not found
+msg.education.added=Education entry added successfully
+msg.work_experience.added=Work experience added successfully
+msg.award.added=Award added successfully
+
+# Education
+msg.education.updated=Education entry updated successfully
+msg.education.deleted=Education entry deleted successfully
+msg.education.not_found=Education entry not found
+
+# Work Experience
+msg.work_experience.updated=Work experience updated successfully
+msg.work_experience.deleted=Work experience deleted successfully
+msg.work_experience.not_found=Work experience not found
+
+# Awards
+msg.award.updated=Award updated successfully
+msg.award.deleted=Award deleted successfully
+msg.award.not_found=Award not found
+
+# Validation
+msg.validation.end_date_before_start=End date cannot be before start date
+msg.validation.future_date=Date cannot be in the future
+
+
+# Validation
+msg.validation.title_required=Resume title is required
+msg.validation.institution_required=Institution name is required
+msg.validation.degree_required=Degree name is required
+msg.validation.start_date_required=Start date is required
+msg.validation.job_title_required=Job title is required
+msg.validation.company_required=Company name is required
+msg.validation.work_start_date_required=Work start date is required
+msg.validation.award_title_required=Award title is required
+msg.validation.issuer_required=Award issuer is required
+msg.validation.issue_date_required=Issue date is required
+msg.validation.failed=Validation failed
+
+# Errors
+msg.generic_error=An error occurred
+
+# retry
+msg.retry=Something went wrong while processing your request. Would you like to retry?
+msg.delete_token_generated=Delete confirmation token generated
+
+# job application
+msg.job_application_submit_success=Job application submitted successfully.
+msg.job_application_count_fetch_success=Job application count retrieved successfully.
+msg.job_application_status_update_success=Job application status updated successfully.
+msg.job_application_retrieve_success=Job applications retrieved successfully.
+msg.job_application_has_applied_check_success=Job application status check successful.
+msg.already_applied=You have already applied for this job.
+msg.candidate_profile_not_found=Candidate profile not found.
+msg.job_application_not_found=Job application not found.
+msg.company_profile_not_found=Company profile not found.
+msg.invalid_role=Invalid user role.
+msg.no_permission=You do not have permission to access this resource
+msg.applicant_ids_required=Applicant IDs are required
+msg.email_subject_required=Email subject is required
+msg.email_body_required=Email body is required
+msg.subcategories_fetched=Job subcategories retrieved successfully.
+msg.resource_subcategories_fetched=Resource subcategories retrieved successfully.
+
+# SEO and Programmatic Pages
+msg.categories_fetched=Categories fetched successfully.
+msg.seo_subcategories_fetched=SEO subcategories fetched successfully.
+msg.seo_subsubcategories_fetched=SEO sub-subcategories fetched successfully.
+msg.programmatic_categories_fetched=Programmatic categories fetched successfully.
+msg.programmatic_subcategories_fetched=Programmatic subcategories fetched successfully.
+msg.programmatic_page_created=Programmatic page created successfully.
+msg.programmatic_page_updated=Programmatic page updated successfully.
+msg.programmatic_page_deleted=Programmatic page deleted successfully.
+msg.programmatic_pages_fetched=Programmatic pages fetched successfully.
+msg.programmatic_page_fetched=Programmatic page fetched successfully.
+msg.city_state_combinations_fetched=Locations fetched successfully.
+msg.seo_metadata_generated=SEO metadata generated successfully
+msg.bulk_seo_metadata_generated=Bulk SEO metadata generated successfully
+msg.error_generating_seo_metadata=Error generating SEO metadata
+msg.error_generating_bulk_seo_metadata=Error generating bulk SEO metadata
+
+
+# contactUs
+msg.contact_us_form_success=Thank you for reaching out! Your message has been sent successfully.
+msg.contact_us_form_error=Something went wrong. Please try again later.
+msg.contact_us_name_mandatory=Name is mandatory.
+msg.contact_us_email_mandatory=Email is mandatory.
+msg.contact_us_email_valid=Email should be valid.
+msg.contact_us_message_mandatory=Message is mandatory.
+
+# s3 bucket
+msg.file_cannot_be_empty=File cannot be empty
+msg.file_uploaded_success=File uploaded successfully
+msg.file_processing_error=File processing error
+msg.file_storage_error=File storage error
+msg.file_deleted=File deleted successfully
+error.file_deletion_failed=Failed to delete file from storage
+
+# auth login
+msg.email_registered_different_role=Email is already registered under a different role.
+msg.email_registered_as_admin=This Email ID is already registered as an Admin; please use the correct login.
+msg.email_registered_as_manager=This Email ID is already registered as a Manager; please use the correct login.
+msg.email_registered_as_staff=This Email ID is already registered as Staff; please use the correct login.
+msg.email_registered_as_recruiter=This Email ID is already registered as a Recruiter; please use the correct login.
+msg.email_registered_as_candidate=This Email ID is already registered as a Candidate; please use the correct login.
+msg.email_registered_as_user=This Email ID is already registered as a User; please use the correct login.
+msg.email_registered_under_different_role=This Email ID is already registered under a different role; please use the correct login.
+msg.success=Operation completed successfully
+msg.mobile_number_required=Mobile number is required
+
+msg.invalid_specialisms=Invalid specialisms provided.
+
+
+msg.checkout.session.created=Checkout session created successfully
+msg.subscription.details.fetched=Subscription details retrieved successfully
+msg.subscription.cancelled=Subscription cancelled successfully
+
+msg.salary_currency_required=Salary currency is required
+msg.pay_type_required=Pay type is required
+msg.invalid_salary_currency=Invalid salary currency
+msg.invalid_pay_type=Invalid pay type
+msg.min_salary_required=Minimum salary is required
+msg.max_salary_required=Maximum salary is required
+
+msg.invalid_deadline_range=Application deadline must be between job opening date and 30 days after
+
+msg.responsibilities_and_benefits_required=Responsibilities and benefits are required
+
+# job applicants
+msg.job_applicants_retrieve_success=Job applicants retrieved successfully.
+msg.company_profile_incomplete=Please complete your company profile to view applicants.
+msg.job_not_owned=You do not have permission to access this job.
+msg.invalid_status=Invalid application status ID provided.
+msg.invalid_job_category_name=Invalid job category name
+msg.jobs_with_applicants_retrieve_success=Jobs with applicants retrieved successfully.
+msg.no_search_parameters=At least one search parameter is required for programmatic page search.
+msg.no_programmatic_pages_found=No programmatic pages found.
+
+msg.job_post_limit_reached=You have reached your job post limit for this month. Please upgrade your subscription or wait until the next month.
+
+
